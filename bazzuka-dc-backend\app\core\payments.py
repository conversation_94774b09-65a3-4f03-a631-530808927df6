import os
import re
import time
import stripe
from datetime import datetime, timedelta

from app.utils.supabase.queries import insert_payment, update_customizations, update_issue_status, mark_action_items_pending, upsert_restrictions, insert_payment_duplicate, update_payment, insert_notification

from app.utils.supabase.queries import get_defaulter_by_id, get_issue_by_id, get_org, mark_defaulter_as_paused, get_defaulter_for_issue, get_payment

from nylas import Client as NylasClient

# from nylas.models.auth import URLForAuthenticationConfig, CodeExchangeRequest
from .ai.payment_analyzer import make_payment_analyzer_client
from datetime import datetime

from .get_info import get_info_email, try_verify_email, get_info_call

from .localstorage import verified_identities

from app.core.stripe import create_invoice, create_stripe_subscription

# Additional imports for functions in the middle of code
from app.utils.supabase.queries import insert_action_item, get_existing_payment_by_issue_amount
SENDGRID_API_KEY = os.environ.get("SENDGRID_API_KEY", "")
VAPI_BASE_URL = os.environ.get("VAPI_BASE_URL", "https://api.vapi.ai")
VAPI_API_KEY = os.environ.get("VAPI_API_KEY", "")
NYLAS_API_KEY = os.environ.get("NYLAS_API_KEY", "")
NYLAS_CLIENT_ID = os.environ.get("NYLAS_CLIENT_ID", "")
NYLAS_API_URI = os.environ.get("NYLAS_API_URI", "https://api.us.nylas.com")

nylas = NylasClient(api_key=NYLAS_API_KEY, api_uri=NYLAS_API_URI)
ai_client = make_payment_analyzer_client()
from app.core.actionitems import ActionItemRepository
from .localstorage import inputs_storage, email_inputs_storage

action_item_repo = ActionItemRepository()

def send_payment_link(payment_id, issue_id, amount, due_date=None):
    try:
        issue = get_issue_by_id(issue_id).data[0]
        defaulter_id = issue["defaulter_id"]
        info = get_defaulter_by_id(defaulter_id).data[0]
        recipient_address = info.get("email")
        recipientName = info.get("name")

        if not recipient_address:
            return "Error: No recipient email found."
    except Exception as e:
        print(f"Error retrieving defaulter information: {e}")
        return f"Error: Failed to retrieve defaulter information - {str(e)}"

    payment = get_payment(payment_id).data[0]
    link = payment.get("url")
    is_recurring = payment.get("recurring", False)
    
    # For recurring payments, ignore due_date and use start_date if needed
    if is_recurring:
        due_date = None
    else:
        # For one-time payments, extract due_date from payment record if not provided
        if due_date is None:
            due_date = payment.get("due_date")

    # If the link is missing or None, generate a new Stripe invoice/subscription and update the payment_links table
    if not link or link == "None":
        amount_cents = int(amount)
        
        try:
            if is_recurring:
                # For recurring payments, create a Stripe subscription
                # Extract interval information from type field (format: "recurring_week_1")
                payment_type = payment.get("type", "recurring_month_1")
                if payment_type.startswith("recurring_") and len(payment_type.split("_")) >= 3:
                    parts = payment_type.split("_")
                    interval = parts[1]  # "week" or "month"
                    interval_count = int(parts[2])  # 1, 2, etc.
                else:
                    # Fallback to monthly
                    interval = "month"
                    interval_count = 1
                
                # Get start_date from payment record if available
                payment_start_date = payment.get("start_date")
                parsed_start_date = None
                if payment_start_date:
                    try:
                        parsed_start_date = datetime.strptime(payment_start_date, "%Y-%m-%d").date()
                    except ValueError:
                        pass
                
                subscription = create_stripe_subscription(issue_id, amount_cents, interval, interval_count, start_date=parsed_start_date)
                
                # For subscriptions, get the hosted invoice URL from the latest invoice
                latest_invoice_id = subscription.get("latest_invoice")
                stripe_invoice_id = None
                if latest_invoice_id:
                    if isinstance(latest_invoice_id, str):
                        invoice_id = latest_invoice_id
                        stripe_invoice_id = invoice_id
                    else:
                        invoice_id = latest_invoice_id.get("id") if hasattr(latest_invoice_id, 'get') else str(latest_invoice_id)
                        stripe_invoice_id = invoice_id
                    
                    retry_count = 0
                    while retry_count < 5:
                        try:
                            invoice = stripe.Invoice.retrieve(invoice_id)
                            link = getattr(invoice, "hosted_invoice_url", None)
                            if link:
                                break
                        except Exception as e:
                            pass
                        
                        time.sleep(2)
                        time.sleep(2)
                        retry_count += 1
                    
                    if not link:
                        return "Error: Could not retrieve a valid payment link for subscription. Email not sent."
                else:
                    return "Error: No invoice found for subscription. Email not sent."
                    
            else:
                # For one-time payments, create a regular invoice
                invoice = create_invoice(issue_id, amount_cents, recurring=False, due_date=due_date, insert_db=False)
                link = getattr(invoice, "hosted_invoice_url", None)
                stripe_invoice_id = getattr(invoice, "id", None)
                retry_count = 0
                while (not link or link == "None") and retry_count < 5:
                    time.sleep(2)
                    invoice = stripe.Invoice.retrieve(invoice.id)
                    link = getattr(invoice, "hosted_invoice_url", None)
                    retry_count += 1
                
                if not link or link == "None":
                    return "Error: Could not retrieve a valid payment link. Email not sent."
            
            # Update the payment_links table with the new url AND stripe_invoice_id
            update_data = {"url": link}
            if stripe_invoice_id:
                update_data["stripe_invoice_id"] = stripe_invoice_id
            update_payment(payment_id, **update_data)
            
        except Exception as e:
            pass
            return f"Error: Failed to create payment link - {str(e)}"

    # Customize email subject and body based on payment type
    if is_recurring:
        # Extract interval information from type field for accurate email text
        payment_type = payment.get("type", "recurring_month_1")
        if payment_type.startswith("recurring_") and len(payment_type.split("_")) >= 3:
            parts = payment_type.split("_")
            interval = parts[1]  # "week" or "month"
            interval_count = int(parts[2])  # 1, 2, etc.
        else:
            # Fallback to monthly
            interval = "month"
            interval_count = 1
        
        if interval == "week" and interval_count == 1:
            frequency_text = "weekly"
        elif interval == "week" and interval_count == 2:
            frequency_text = "biweekly"
        elif interval == "month" and interval_count == 1:
            frequency_text = "monthly"
        else:
            frequency_text = f"every {interval_count} {interval}(s)"
            
        subject = f"Your {frequency_text.title()} Payment Setup"
        body = f"""
        <div style="max-width: 600px; margin: 0 auto; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
                <h1 style="color: white; margin: 0; font-size: 24px; font-weight: 600;">Payment Setup Required</h1>
            </div>
            
            <div style="background: #ffffff; padding: 40px 30px; border: 1px solid #e1e5e9; border-top: none;">
                <p style="font-size: 16px; margin-bottom: 20px; color: #2c3e50;">
                    Hello {recipientName.split()[0]},
                </p>
                
                <p style="font-size: 16px; margin-bottom: 25px; color: #34495e;">
                    We've prepared a secure payment setup for your <strong>{frequency_text}</strong> payment plan. 
                    This will automatically process <strong>${"{:.2f}".format(amount/100)}</strong> on a {frequency_text} basis.
                </p>
                
                <div style="text-align: center; margin: 35px 0;">
                    <a href="{link}" 
                       style="display: inline-block; background-color: #667eea; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                              color: white !important; text-decoration: none; padding: 15px 30px; border-radius: 6px; 
                              font-weight: 600; font-size: 16px; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
                              transition: all 0.3s ease;">
                        Set Up {frequency_text.title()} Payments →
                    </a>
                </div>
                
                <p style="font-size: 14px; color: #7f8c8d; margin-top: 30px;">
                    If you have any questions about this payment plan, please don't hesitate to reply to this email.
                </p>
                
                <p style="font-size: 16px; margin-top: 25px; color: #2c3e50;">
                    Best regards,<br>
                    <strong>Bazzuka</strong>
                </p>
            </div>
        </div>
        """
    else:
        subject = "Payment Required - Secure Link Enclosed"
        body = f"""
        <div style="max-width: 600px; margin: 0 auto; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333;">
            <div style="background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
                <h1 style="color: white; margin: 0; font-size: 24px; font-weight: 600;">Payment Required</h1>
            </div>
            
            <div style="background: #ffffff; padding: 40px 30px; border: 1px solid #e1e5e9; border-top: none;">
                <p style="font-size: 16px; margin-bottom: 20px; color: #2c3e50;">
                    Hello {recipientName.split()[0]},
                </p>
                
                <p style="font-size: 16px; margin-bottom: 25px; color: #34495e;">
                    Your one-time payment of <strong>${"{:.2f}".format(amount/100)}</strong> has been approved. 
                    Please use the secure payment link below to complete your transaction.
                </p>
                
                <div style="text-align: center; margin: 35px 0;">
                    <a href="{link}" 
                       style="display: inline-block; background-color: #4CAF50; background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); 
                              color: white !important; text-decoration: none; padding: 15px 30px; border-radius: 6px; 
                              font-weight: 600; font-size: 16px; box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
                              transition: all 0.3s ease;">
                        Pay ${"{:.2f}".format(amount/100)} Now →
                    </a>
                </div>
                
                <p style="font-size: 14px; color: #7f8c8d; margin-top: 30px;">
                    If you experience any issues with this payment link or have questions about your account, 
                    please contact our support team immediately.
                </p>
                
                <p style="font-size: 16px; margin-top: 25px; color: #2c3e50;">
                    Thank you for your prompt attention,<br>
                    <strong>Bazzuka</strong>
                </p>
            </div>
        </div>
        """

    # Send payment link

    message_id = send(
        {
            "recipientAddress": recipient_address,
            "recipientName": recipientName,
            "subject": subject,
            "text": body,
        }
    )

    update_issue_status(issue_id, "in-progress")
    return message_id

def parse_money_to_cents(text: str) -> int:
    """
    Converts a string like "$1,234.56" to an integer value in cents: 123456
    """
    # Remove anything that's not a digit or dot
    clean = re.sub(r'[^\d.]', '', text)
    
    # Handle decimal point if it exists
    if '.' in clean:
        dollars, cents = clean.split('.')
        cents = (cents + '00')[:2]  # pad/truncate to 2 digits
    else:
        dollars, cents = clean, '00'
    
    return int(dollars) * 100 + int(cents)

# def transform_for_llm(data):
#     # Parse nested case_info if it's a string representation of a dict
#     if isinstance(data.get("case_info"), str):
#         try:
#             data["case_info"] = eval(data["case_info"])
#             # pop customizations from case_info
#             data["case_info"].pop("customizations", None)
#         except Exception:
#             pass

#     # Format output
#     output = "\nNegotiation Strategy:\n"
#     output += data["strategy"]

#     output += "\nCommunication History:\n"
#     for comm in data["commlogs"]:
#         output += f"{comm['channel'].upper()} ({comm['direction']}) - {comm['timestamp']}:\n"
#         output += f"Summary: {comm['summary']}\n"
#         output += "-" * 10 + "\n"

#     output += "Most Recent Case Information:\n"
#     for issue in data["case_info"].get("cases", []):
#         output += f"\nAccount {issue['issue_id']} =========\n"
#         output += f"issue_id: {issue['issue_id']}\n"
#         output += f"Outstanding Amount: {issue['outstanding_amount']}\n"
#         output += f"Delinquency Date: {issue['delinquency_date']}\n"
#         output += f"Status: {'Request payment and negotiate' if issue['status'] == 'unsolved' else 'This account is currently in good standing.'}\n"
#         output += f"Additional Data: {str(issue.get('metadata', {}))}\n"
#         output += "=========\n"

#     if data.get("reason"):
#         output += f"\nThe purpose of this communication is: {data['reason']}"

#     print(output)
#     return output


def send(data):
    subject = data["subject"]
    text = data["text"]
    recipient_name = data.get("recipientName", "New Customer")
    recipient_address = data["recipientAddress"]

    # Temporarily hardcoded grant ID
    grant_id = "490ffcaa-0de8-4a0e-951b-c652b570e14d"

    # Construct a cleaner and more structured HTML email body
    body = {
        "subject": subject,
        "body": f"""
        <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ padding: 20px; }}
                    a {{ color: #1a73e8; text-decoration: none; }}
                </style>
            </head>
            <body>
                <div class="container">
                    {text}
                </div>
            </body>
        </html>
        """,
        "to": [{"name": recipient_name, "email": recipient_address}],
    }

    if "reply_to_message_id" in data:
        body["reply_to_message_id"] = data["reply_to_message_id"]

    message = nylas.messages.send(grant_id, request_body=body).data

    return message.id


def format_date(date_str):
    date_obj = datetime.strptime(date_str, "%Y-%m-%d")
    return date_obj.strftime("%B %d, %Y")


# def format_email_args_readable(args):
#     inputs = "Case Info:\n"
#     print(args["case_info"])
#     for issue in args["case_info"].get("cases", []):
#         inputs += f"\nAccount {issue['issue_id']} =========\n"
#         inputs += f"issue_id: {issue['issue_id']}\n"
#         inputs += f"Outstanding Amount: {issue['outstanding_amount']}\n"
#         inputs += f"Delinquency Date: {issue['delinquency_date']}\n"
#         inputs += f"Status: {'Request payment and negotiate' if issue['status'] == 'unsolved' else 'This account is currently in good standing.'}\n"
#         inputs += f"Additional Data: {str(issue.get('metadata', {}))}\n"
#         inputs += "=========\n"

#     if args.get("strategy"):
#         inputs += f"\nThe negotiation strategy is:\n{args['strategy']}\n"
#     else:
#         inputs += "\nNo negotiation strategy provided.\n"

#     # Format communication history
#     comm_history = args.get("commlogs", [])
#     inputs += "\n============= Begin Communication History ==============\n"
#     if comm_history:
#         for comm in comm_history:
#             if comm["type"] == "call":
#                 inputs += f"Call ({comm['direction']}) at {comm['timestamp']}:\nSummary: {comm['summary']}\n"
#             elif comm["type"] == "email":
#                 inputs += f"Email ({comm['direction']}) at {comm['timestamp']}:\nContent: {comm['content']}\n"
#     else:
#         inputs += "There has been no communication with this customer. This is the first contact."
#     inputs += "============= End of Communication History ==============\n"
    
#     # Format inbound message section if present
#     inbound_msg = args.get("inbound_msg")
#     if inbound_msg:
#         inputs += f"\nCompose a **reply** to this inbound message given the context of the communication history above:\n{inbound_msg}"

#     if args.get("reason"):
#         inputs += f"\nThe purpose of this communication is: {args['reason']}"

#     return inputs


class PaymentsTool:
    def __init__(self):
        pass

    # def get_info_email(self, defaulter_id):
        # # Get organization settings
        # org_metadata = get_org().data[0]["metadata"]
        # verification_types = org_metadata.get("verification_types", [])

        # # If no verification types are configured, consider verified
        # if not verification_types:
        #     result = email_inputs_storage.get(defaulter_id)
        #     if result:
        #         return format_email_args_readable(result)
        #     else:
        #         return "Error: No information found for this defaulter."

        # # Check if user is verified
        # result = verified_identities.get(defaulter_id)
        # if result:
        #     return format_email_args_readable(result)
        # else:
        #     return "Error: This user is not verified. Proceed verifying the user's identity using the 'try_verify' tool."

    def get_info_email(self, defaulter_id):
        return get_info_email(defaulter_id)

    # def try_verify(self, defaulter_id, verification_type, verification_value):
    #     # Get organization settings
    #     org_metadata = get_org().data[0]["metadata"]
    #     verification_types = org_metadata.get("verification_types", [])
    
    #     # If no verification types are configured, return info without verification
    #     if not verification_types:
    #         result = email_inputs_storage.get(defaulter_id)
    #         if result:
    #             verified_identities.set(defaulter_id, result)
    #             return format_email_args_readable(result)
    #         return "Error: No information found for this defaulter."

    #     # If verification is required but not provided
    #     if not verification_type or not verification_value:
    #         return "Error: Verification is required. Please provide verification details."

    #     # Verify and return info
    #     result = email_inputs_storage.get(defaulter_id, verification_type, verification_value)
    #     if result:
    #         verified_identities.set(defaulter_id, result)
    #         return format_email_args_readable(result)
    #     return "Error: Identity verification failed."

    def try_verify(self, defaulter_id, verification_type=None, verification_value=None):
        return try_verify_email(defaulter_id, verification_type=verification_type, verification_value=verification_value)

    def schedule_one_off_communication(self, defaulter_id, channel, date, time, reason, is_human_followup=False):
        # print("WARN: we assume the payment likelihood is 3")
        created = action_item_repo.create(
            date, time, channel, defaulter_id, reason, None, "customer-requested", is_human_followup
        )
        if created:
            return "Success: One-off communication scheduled."
        else:
            return "Error: Failed to schedule one-off communication."

    # def get_info(self, defaulter_id, verification_type=None, verification_value=None):
    #     # Get organization settings
    #     org_metadata = get_org().data[0]["metadata"]
    #     verification_types = org_metadata.get("verification_types", [])
    #     print("Made it.")
    #     # If no verification types are configured, return info without verification
    #     if not verification_types:
    #         print("Made it 2.")
    #         result = inputs_storage.get(defaulter_id)
    #         print("Debug - result from inputs_storage.get():", result)
    #         print("Debug - type of result:", type(result))
    #         if result:
    #             print("Made it 3.")
    #             print("Debug - result contents:", result)
    #             return transform_for_llm(result)
    #         return "Error: No information found for this defaulter."

    #     # If verification is required but not provided
    #     if not verification_type or not verification_value:
    #         return "Error: Verification is required. Please provide verification details."

    #     # Verify and return info
    #     result = inputs_storage.get(defaulter_id, verification_type, verification_value)
    #     if result:
    #         return transform_for_llm(result)
    #     return "Error: Identity verification failed."

    def get_info(self, defaulter_id, verification_type=None, verification_value=None):
        return get_info_call(defaulter_id, verification_type=verification_type, verification_value=verification_value)

    def opt_out_of_communications(self, defaulter_id, channels):
        """
        Opt a defaulter out of specific communication channels.
        
        Args:
            defaulter_id (str): The ID of the defaulter
            channels (list): List of channels to opt out of (e.g. ["calls", "emails", "texts"])
        """
        # Get current customizations
        defaulter = get_defaulter_by_id(defaulter_id).data[0]
        current_customizations = defaulter.get("customizations", {})
        
        # Get existing blocked channels or initialize empty list
        existing_channels = current_customizations.get("blocked_channels", [])
        
        # Combine existing and new channels, removing duplicates
        updated_channels = list(set(existing_channels + channels))
        
        # Update blocked_channels
        current_customizations["blocked_channels"] = updated_channels
        
        # Update customizations in database
        update_customizations(defaulter_id, current_customizations)
        
        return f"Successfully opted out"

    def schedule_payment(self, issue_id, amount, recurring=False, settlement_discount=False, interval=None, interval_count=None, start_date=None, due_date=None):
        """
        Schedule a payment for approval. Creates a payment record in pending status.
        Payment link generation and email sending will happen only after approval.
        For one-time full payments, bypasses approval and sends payment link directly.
        interval: 'month', 'week', etc. (for recurring payments)
        interval_count: 1 for monthly/weekly, 2 for biweekly, etc.
        start_date: When the payment plan should begin (YYYY-MM-DD format)
        due_date: When the payment is due (YYYY-MM-DD format)
        """
        try:
            today = datetime.now().date()
            
            # Parse dates if provided
            parsed_start_date = None
            parsed_due_date = None
            
            if start_date:
                try:
                    parsed_start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
                    
                    # CRITICAL: Validate start_date is not in the past
                    if parsed_start_date < today:
                        return f"Error: start_date ({start_date}) cannot be in the past. Today is {today}. Please use a current or future date."
                        
                except ValueError:
                    return "Error: Invalid start_date format. Please use YYYY-MM-DD format."
            
            if due_date:
                try:
                    parsed_due_date = datetime.strptime(due_date, "%Y-%m-%d").date()
                    
                    # CRITICAL: Validate due_date is not in the past
                    if parsed_due_date < today:
                        return f"Error: due_date ({due_date}) cannot be in the past. Today is {today}. Please use a current or future date."
                        
                except ValueError:
                    return "Error: Invalid due_date format. Please use YYYY-MM-DD format."
            
            # Additional validation: start_date should not be after due_date
            if parsed_start_date and parsed_due_date and parsed_start_date > parsed_due_date:
                return f"Error: start_date ({start_date}) cannot be after due_date ({due_date}). Please adjust the dates."

            # Smart defaults: Different logic for one-time vs recurring payments
            if recurring:
                # For RECURRING PAYMENTS: Ignore due_date, use start_date as the first due date
                if not parsed_start_date:
                    parsed_start_date = today
                    start_date = parsed_start_date.strftime("%Y-%m-%d")
                # The first due date is the start_date
                due_date = None
                parsed_due_date = None
            else:
                # ONE-TIME PAYMENTS: start_date is optional (for delayed sending)
                if not parsed_due_date:
                    if parsed_start_date:
                        # If start_date provided, assume due_date is 7 days later
                        parsed_due_date = parsed_start_date + timedelta(days=7)
                        due_date = parsed_due_date.strftime("%Y-%m-%d")
                    else:
                        # No dates provided - due in 7 days, send link now
                        parsed_due_date = today + timedelta(days=7)
                        due_date = parsed_due_date.strftime("%Y-%m-%d")
                # For one-time payments, start_date is optional and means "when to send link"

            amount_cents = int(amount * 100)
            issue = get_issue_by_id(issue_id).data[0]
            defaulter_id = issue["defaulter_id"]
            info = get_defaulter_by_id(defaulter_id).data[0]

            # Validate defaulter info
            if not info:
                return "Error: Defaulter information not found."

            recipient_address = info.get("email")
            recipientName = info.get("name", "Customer")

            # Recipient details

            if not recipient_address:
                return "Error: No recipient email found."

            # Update issue status before creating payment
            update_issue_status(issue_id, "in-progress")

            if recurring:
                # Auto-detect interval and interval_count if not provided
                if not interval or not interval_count:
                    interval = "month"
                    interval_count = 1

                # Validate interval and interval_count
                valid_intervals = ["month", "week"]
                if interval not in valid_intervals:
                    return f"Error: Invalid interval. Must be one of {valid_intervals}"
                if interval_count < 1:
                    return "Error: Interval count must be at least 1"

                # Recurring payment scheduled
                
                # Create payment record in pending status - NO Stripe creation yet
                # Note: interval and interval_count will be handled during approval
                db_result = insert_payment_duplicate(
                    amount=amount_cents,
                    issue_id=issue_id,
                    recurring=recurring,
                    approved=False,  # This will set _approved to "pending"
                    settlement_discount=settlement_discount,
                    stripe_invoice_id=None,
                    url=None,
                    type=f"recurring_{interval}_{interval_count}",  # Store interval info in type field
                    due_date=None,  # No due_date for recurring, only start_date
                    start_date=start_date,
                    is_promise=True  # This is a promise-to-pay arrangement
                )
                
                # Note: interval and interval_count information will be passed during approval
                # The approval endpoint will use these values when creating the Stripe subscription
                
                # Clear messaging for recurring payments
                response_msg = f"Recurring {interval}ly payment plan of ${amount_cents/100:.2f} has been scheduled for approval."
                
                if parsed_start_date and parsed_start_date > today:
                    response_msg += f" Billing will start on {start_date}. A human will review and approve before activation."
                else:
                    response_msg += f" Billing will start immediately after approval."
                
                if db_result and db_result.data:
                    insert_notification(defaulter_id, f"Payment plan for {info['name']} (id: {defaulter_id}) for ${amount_cents/100:.2f} pending approval")
                
                return response_msg
                
            else:
                # Check if this is a full payment (amount >= outstanding amount)
                outstanding_amount_cents = parse_money_to_cents(issue.get("outstanding_amount", "$0.00"))
                
                # STRICT VALIDATION: Only allow exact payments or partial payments
                # Directly reject any amount higher than outstanding balance
                is_exact_payment = amount_cents == outstanding_amount_cents
                is_partial_payment = amount_cents < outstanding_amount_cents
                is_overcharge = amount_cents > outstanding_amount_cents
                
                # Payment validation
                
                # DIRECTLY REJECT any payment that exceeds outstanding balance
                if is_overcharge:
                    overcharge_amount = amount_cents - outstanding_amount_cents
                    return f"ERROR: Payment REJECTED. Cannot process payment of ${amount:.2f} because it exceeds the outstanding balance of ${outstanding_amount_cents/100:.2f} by ${overcharge_amount/100:.2f}. Please schedule a payment for the exact outstanding amount (${outstanding_amount_cents/100:.2f}) or less."
                
                # Only allow exact payments for automatic processing
                elif is_exact_payment:
                    # For exact payments only, bypass approval and create payment link directly
                    try:
                        # Create payment record as approved
                        db_result = insert_payment_duplicate(
                            amount=amount_cents,
                            issue_id=issue_id,
                            recurring=recurring,
                            approved=True,  # Set as approved for exact payments only
                            settlement_discount=settlement_discount,
                            stripe_invoice_id=None,
                            url=None,
                            type="one_time",
                            due_date=due_date,
                            start_date=start_date,
                            is_promise=True  # This is a promise-to-pay arrangement
                        )
                        
                        if not db_result.data:
                            return "Error: Failed to create payment record."
                        
                        payment_id = db_result.data[0]["id"]
                        
                        # Check if start_date is in the future (delayed link sending)
                        if parsed_start_date and parsed_start_date > today:
                            # Schedule action item to send link on start_date
                            try:
                                insert_action_item(
                                    action_date=start_date,
                                    action_time="10:00:00",
                                    action_channel="email",
                                    action_channel_content="",
                                    defaulter_id=defaulter_id,
                                    action_reason=f"Send payment link for ${amount:.2f} payment due {due_date}",
                                    payment_likelihood=3,
                                    category="payment_link",
                                    _approved=True
                                )
                                return f"Payment link for ${amount_cents/100:.2f} will be sent on {start_date}. Payment due by {due_date}."
                            except Exception as e:
                                pass
                                # Fall through to immediate sending
                        
                        # Send payment link immediately (normal case)
                        result = send_payment_link(payment_id, issue_id, amount_cents, due_date=parsed_due_date)
                        
                        # Check if send_payment_link was successful
                        if isinstance(result, str) and result.startswith("Error:"):
                            return f"Error: Failed to send payment link - {result}"
                        
                        due_date_msg = f" due by {due_date}" if due_date else ""
                        return f"Payment link for ${amount:.2f}{due_date_msg} has been sent to {recipient_address}."
                        
                    except Exception as e:
                        return f"Error: Failed to create exact payment link - {str(e)}"
                else:
                    # For partial payments (amount < outstanding), use the existing approval flow
                    db_result = insert_payment_duplicate(
                        amount=amount_cents,
                        issue_id=issue_id,
                        recurring=recurring,
                        approved=False,  # This will set _approved to "pending"
                        settlement_discount=settlement_discount,
                        stripe_invoice_id=None,
                        url=None,
                        type="one_time",
                        due_date=due_date,
                        start_date=start_date,
                        is_promise=True  # This is a promise-to-pay arrangement
                    )
                    
                    # Handle delayed sending for partial payments
                response_msg = f"Partial payment has been scheduled for approval. A human will review and approve this payment of ${amount:.2f}"
                
                if parsed_start_date and parsed_start_date > today:
                    # For partial payments with future start_date, schedule link sending
                    response_msg += f". Payment link will be sent on {start_date} after approval"
                    if due_date:
                        response_msg += f", due by {due_date}"
                    response_msg += "."
                else:
                    # Normal case: send immediately after approval
                    response_msg += f" before the payment link is sent to {recipient_address}"
                    if due_date:
                        response_msg += f" (due by {due_date})"
                    response_msg += "."
                
                if db_result and db_result.data:
                    insert_notification(defaulter_id, f"Payment plan for {info['name']} (id: {defaulter_id}) for ${amount_cents/100:.2f} pending approval")
                
                return response_msg

        except Exception as e:
            return f"Error: An unexpected error occurred while scheduling payment. {str(e)}"
    

    def setup_payment_and_get_link(self, issue_id, amount, recurring=False, settlement_discount=False):
        """
        Create a Stripe invoice and return the payment link (hosted_invoice_url).
        """
        import time
        
        amount_cents = int(amount * 100)
        
        # Create Stripe invoice without inserting to DB (to avoid duplicates)
        invoice = create_invoice(issue_id, amount_cents, recurring=recurring, insert_db=False)
        link = getattr(invoice, "hosted_invoice_url", None)
        stripe_invoice_id = getattr(invoice, "id", None)
        
        retry_count = 0
        # Retry up to 5 times if link is missing
        while (not link or link == "None") and retry_count < 5:
            time.sleep(2)
            invoice = stripe.Invoice.retrieve(invoice.id)
            link = getattr(invoice, "hosted_invoice_url", None)
            retry_count += 1
        
        if not link or link == "None":
            return "Error: Could not retrieve a valid payment link."
        
        # Check if a payment record already exists for this issue/amount
        
        # Look for existing payment for this issue and amount
        existing_payment = get_existing_payment_by_issue_amount(issue_id, amount_cents, recurring)
        
        if existing_payment.data:
            # Update existing payment with stripe_invoice_id and url
            payment_id = existing_payment.data[0]['id']
            update_payment(payment_id, stripe_invoice_id=stripe_invoice_id, url=link)
            # print(f"[SETUP_FIX] Updated existing payment {payment_id} with stripe_invoice_id: {stripe_invoice_id}")
        else:
            # Create new payment record with stripe_invoice_id
            insert_payment_duplicate(
                amount=amount_cents,
                issue_id=issue_id,
                recurring=recurring,
                approved=True,  # setup_payment_and_get_link implies immediate use
                settlement_discount=settlement_discount,
                stripe_invoice_id=stripe_invoice_id,
                url=link,
                type="recurring" if recurring else "one_time",
                due_date=None,
                start_date=None,
                is_promise=True  # This is a promise-to-pay arrangement
            )
            # print(f"[SETUP_FIX] Created new payment with stripe_invoice_id: {stripe_invoice_id}")
        
        update_issue_status(issue_id, "in-progress")
        return link
    
    def restrict_communication(self, defaulter_id, description):
        """
        Restrict a defaulter's communication by day of the week, time of the day, and/or holidays.
        
        Args:
            defaulter_id (str): The ID of the defaulter
            description (str): A description of the communication restrictions
        """
        # Update customizations in database
        upsert_restrictions(defaulter_id, description)
        
        return f"Successfully restricted communication for {defaulter_id} with description: {description}"

    def mark_disputed(self, issue_id):
        """
        Pause the defaulter's communication if the account is disputed (e.g., wrong person contacted).
        Args:
            issue_id (str or int): The issue ID to identify the defaulter.
        Returns:
            str: Success or error message.
        """
        # Get the defaulter_id for the given issue_id
        result = get_defaulter_for_issue(issue_id)
        if not result or not result.data or not result.data[0].get("defaulter_id"):
            return f"Error: Could not find defaulter for issue_id {issue_id}"
        defaulter_id = result.data[0]["defaulter_id"]
        # Pause the defaulter
        mark_defaulter_as_paused(defaulter_id)
        return f"Defaulter {defaulter_id} has been paused due to dispute on issue {issue_id}."

    def request_cancel_payment_arrangement(self, payment_id: str, reason: str = None):
        """
        Request cancellation of a payment arrangement by setting _approved to 'pending_cancellation'.
        Optionally record a reason/comment.
        """
        updates = {"_approved": "pending_cancellation"}
        # Note: cancellation_reason column doesn't exist in the schema, so we skip it
        try:
            update_payment(payment_id, **updates)
            return f"Payment arrangement {payment_id} has been marked as pending_cancellation. It will require human approval before final cancellation."
        except Exception as e:
            return f"Error: Could not mark payment arrangement {payment_id} as pending_cancellation: {e}"

    def send_payment_links(self, issue_id):
        """
        Resend all approved and unpaid payment links for an issue to the defaulter's email.

        Args:
            issue_id (str): The issue_id to send the associated link(s) for.

        Returns:
            str: Success or error message
        """
        try:
            # Convert issue_id to int if it's a string
            issue_id = int(issue_id)

            # Get all payment links for this issue
            payments_result = get_payments_by_issue_id(issue_id)
            if not payments_result.data:
                return f"No payment links found for issue {issue_id}."

            # Filter for approved and unpaid payment links
            approved_unpaid_payments = [
                payment for payment in payments_result.data
                if payment.get("_approved") == "approved" and payment.get("status") != "paid"
            ]

            if not approved_unpaid_payments:
                return f"No approved and unpaid payment links found for issue {issue_id}."

            # Get defaulter information for email
            issue = get_issue_by_id(issue_id).data[0]
            defaulter_id = issue["defaulter_id"]
            info = get_defaulter_by_id(defaulter_id).data[0]
            recipient_address = info.get("email")

            if not recipient_address:
                return "Error: No recipient email found for this defaulter."

            # Send each payment link
            sent_count = 0
            errors = []

            for payment in approved_unpaid_payments:
                payment_id = payment["id"]
                amount = payment["amount"]
                due_date = payment.get("due_date")

                # Use the existing send_payment_link function
                result = send_payment_link(payment_id, issue_id, amount, due_date)

                if isinstance(result, str) and result.startswith("Error:"):
                    errors.append(f"Payment {payment_id}: {result}")
                else:
                    sent_count += 1

            # Return summary
            if errors:
                error_summary = "; ".join(errors)
                if sent_count > 0:
                    return f"Sent {sent_count} payment link(s) successfully. Errors: {error_summary}"
                else:
                    return f"Failed to send payment links. Errors: {error_summary}"
            else:
                return f"Successfully resent {sent_count} payment link(s) to {recipient_address}."

        except Exception as e:
            return f"Error: Failed to resend payment links for issue {issue_id} - {str(e)}"

    # LEGACY CODE: Original payment plan and link generation using generic URLs
    # This code has been replaced with Stripe invoice-based payment system
    # Keeping commented for reference during transition period
    # def create_and_send_payment_link(self, issue_id, amounts, due_dates):

    #     if get_payment_plan_for_issue(issue_id).data:
    #         return "Error: Payment plan already exists."

    #     payment_plan = insert_payment_plan({
    #         "issue_id": issue_id,
    #         "status": "active"
    #     })

    #     payment_plan_id = payment_plan.data[0]["id"]

    #     print("Creating payment link...")

    #     # TODO: combine these two queries into one
    #     issue = get_issue_by_id(issue_id).data[0]
    #     defaulter_id = issue["defaulter_id"]
    #     info = get_defaulter_by_id(defaulter_id).data[0]

    #     if info.get("customizations"):
    #         info.pop("customizations", None)

    #     recipient_address = info.get("email")
    #     recipientName = info.get("name")
    #     if not recipient_address:
    #         return "Error: No recipient email found."

    #     # Insert payment and generate link
    #     # TODO: add all scheduled payments at once instead of just the first one
    #     payments = []   
    #     for i in range(len(amounts)):
    #         payments.append({
    #             "defaulter_id": defaulter_id,
    #             "amount": amounts[i],
    #             "due_date": due_dates[i],
    #             "issue_id": issue_id,
    #             "payment_plan": payment_plan_id,
    #             "status": "unpaid"
    #         })

    #     response = insert_payments(payments)

    #     uuid = response.data[0]["uuid"]
    #     link = f"https://sscd05m3-5000.use.devtunnels.ms/v0/pay/{uuid}"

    #     # get all uuids
    #     uuids = [payment["uuid"] for payment in response.data]
    #     links = [f"https://sscd05m3-5000.use.devtunnels.ms/v0/pay/{uuid}" for uuid in uuids]

    #     # Compose and send the email
    #     subject = "Your Payment Link"
    #     body = f'Please use the following link to complete your upcoming payment of ${str(amounts[0]/100)} due on {format_date(due_dates[0])}: <a href="{link}">{link}</a>'

    #     message_id = send(
    #         {
    #             "recipientAddress": recipient_address,
    #             "recipientName": recipientName,
    #             "subject": subject,
    #             "text": body,
    #         }
    #     )

    #     due_dates = [datetime.strptime(date, "%Y-%m-%d") for date in due_dates]
    #     # amounts from cents to dollars
    #     # TODO: use decimal instead of float
    #     amounts = [str(amount / 100) for amount in amounts]

    #     action_items = []
    #     payment_comms = []
    #     for i in range(len(due_dates)):
    #         # if due_dates[i] > datetime.now() + timedelta(days=1):
    #         if i > 0:
    #             action_items.append({
    #                 "action_date": (due_dates[i] - timedelta(days=1)).strftime("%Y-%m-%d"),
    #                 "action_time": "10:00:00",
    #                 "action_channel": "email",
    #                 "defaulter_id": defaulter_id,
    #                 "action_reason": "This is a periodic payment reminder scheduled to send one day before the $"
    #                 + "{:.2f}".format(float(amounts[i]))
    #                 + " payment is due on "
    #                 + due_dates[i].strftime("%Y-%m-%d")
    #                 + f" to ensure the payment is made on time. The link is {links[i]}.",
    #                 "category": "payment_reminder",
    #             })

    #             payment_comms.append({
    #                 "payment": uuids[i],
    #                 "payment_plan": payment_plan_id
    #             })

    #         if i + 1 < len(due_dates):
    #             action_items.append({
    #                 "action_date": (due_dates[i] + timedelta(days=1)).strftime("%Y-%m-%d"),
    #                 "action_time": "10:00:00",
    #                 "action_channel": "email",
    #                 "defaulter_id": defaulter_id,
    #                 "action_reason": "This is a payment link for the $"
    #                 + "{:.2f}".format(float(amounts[i + 1]))
    #                 + f" payment due on {due_dates[i + 1].strftime("%Y-%m-%d")}. It is scheduled to be sent immediately after the previous payment's due date to encourage proactive decision-making and provide ample time for the recipient to organize their payment arrangements, ensuring a smooth and timely payment process. The link is {links[i+1]}.",
    #                 "category": "payment_link",
    #             })

    #             payment_comms.append({
    #                 "payment": uuids[i+1],
    #                 "payment_plan": payment_plan_id
    #             })

    #     action_items = insert_action_items(action_items).data

    #     for i in range(len(payment_comms)):
    #         payment_comms[i]["action_item"] = action_items[i]["id"]

    #     insert_payment_comms(payment_comms)
    #     update_issue_status(issue_id, "in-progress")
    #     if message_id:
    #         return f"The payment link was successfully sent to {recipient_address}."
    #     else:
    #         return "Error: Failed to send the payment link."
        

    # def delete_payment_plan_for_issue(self, issue_id):
    #     payment_plan = get_payment_plan_for_issue(issue_id).data[0]
    #     if payment_plan:
    #         action_items = get_action_items_for_payment_plan(payment_plan["id"]).data
    #         action_item_ids = [action_item["action_item"] for action_item in action_items]
    #         delete_payment_comms(action_item_ids) 
    #         delete_action_items(action_item_ids)
    #         update_payments_status_deleted(payment_plan["id"])
    #         deactivate_payment_plan(payment_plan["id"])
    #         # TODO: update_issue_status may need fourth category for payment plan that started but was not completed
    #         update_issue_status(issue_id, "unsolved")
    #         return "The payment plan was successfully deleted."
    #     else:
    #         return "Error: No payment plan found for this issue."

    # def get_upcoming_payments(self, from_date: str = None, to_date: str = None, limit: int = 8):
    #     return get_upcoming_payments(from_date, to_date, limit)

    # def setup_payments_and_get_payment_link(self, issue_id, amounts, due_dates):
    #     # Check if payment plan already exists
    #     if get_payment_plan_for_issue(issue_id).data:
    #         return "Error: Payment plan already exists."

    #     # Create payment plan
    #     payment_plan = insert_payment_plan({
    #         "issue_id": issue_id,
    #         "status": "active"
    #     })
    #     payment_plan_id = payment_plan.data[0]["id"]

    #     # Get issue and defaulter info
    #     issue = get_issue_by_id(issue_id).data[0]
    #     defaulter_id = issue["defaulter_id"]

    #     # Create payments array
    #     payments = []
    #     for i in range(len(amounts)):
    #         payments.append({
    #             "defaulter_id": defaulter_id,
    #             "amount": amounts[i],
    #             "due_date": due_dates[i],
    #             "issue_id": issue_id,
    #             "payment_plan": payment_plan_id,
    #             "status": "unpaid"
    #         })

    #     # Insert payments and get UUIDs
    #     response = insert_payments(payments)
    #     uuids = [payment["uuid"] for payment in response.data]
    #     links = [f"https://sscd05m3-5000.use.devtunnels.ms/v0/pay/{uuid}" for uuid in uuids]

    #     # Convert dates and amounts for action items
    #     due_dates = [datetime.strptime(date, "%Y-%m-%d") for date in due_dates]
    #     amounts = [str(amount / 100) for amount in amounts]

    #     # Create action items for payment reminders
    #     action_items = []
    #     payment_comms = []
        
    #     # Always create an action item for the first payment
    #     action_items.append({
    #         "action_date": (due_dates[0] - timedelta(days=1)).strftime("%Y-%m-%d"),
    #         "action_time": "10:00:00",
    #         "action_channel": "email",
    #         "defaulter_id": defaulter_id,
    #         "action_reason": "This is a periodic payment reminder scheduled to send one day before the $"
    #         + "{:.2f}".format(float(amounts[0]))
    #         + " payment is due on "
    #         + due_dates[0].strftime("%Y-%m-%d")
    #         + f" to ensure the payment is made on time. The link is {links[0]}.",
    #         "category": "payment_reminder",
    #     })
    #     payment_comms.append({
    #         "payment": uuids[0],
    #         "payment_plan": payment_plan_id
    #     })

    #     # Create action items for remaining payments
    #     for i in range(1, len(due_dates)):
    #         # Add reminder one day before payment
    #         action_items.append({
    #             "action_date": (due_dates[i] - timedelta(days=1)).strftime("%Y-%m-%d"),
    #             "action_time": "10:00:00",
    #             "action_channel": "email",
    #             "defaulter_id": defaulter_id,
    #             "action_reason": "This is a periodic payment reminder scheduled to send one day before the $"
    #             + "{:.2f}".format(float(amounts[i]))
    #             + " payment is due on "
    #             + due_dates[i].strftime("%Y-%m-%d")
    #             + f" to ensure the payment is made on time. The link is {links[i]}.",
    #             "category": "payment_reminder",
    #         })
    #         payment_comms.append({
    #             "payment": uuids[i],
    #             "payment_plan": payment_plan_id
    #         })

    #         if i + 1 < len(due_dates):
    #             # Add payment link for next payment
    #             action_items.append({
    #                 "action_date": (due_dates[i] + timedelta(days=1)).strftime("%Y-%m-%d"),
    #                 "action_time": "10:00:00",
    #                 "action_channel": "email",
    #                 "defaulter_id": defaulter_id,
    #                 "action_reason": "This is a payment link for the $"
    #                 + "{:.2f}".format(float(amounts[i + 1]))
    #                 + f" payment due on {due_dates[i + 1].strftime("%Y-%m-%d")}. It is scheduled to be sent immediately after the previous payment's due date to encourage proactive decision-making and provide ample time for the recipient to organize their payment arrangements, ensuring a smooth and timely payment process. The link is {links[i+1]}.",
    #                 "category": "payment_link",
    #             })
    #             payment_comms.append({
    #                 "payment": uuids[i+1],
    #                 "payment_plan": payment_plan_id
    #             })

    #     # Insert action items and payment communications
    #     if action_items:  # Only insert if we have action items
    #         action_items = insert_action_items(action_items).data
    #         for i in range(len(payment_comms)):
    #             payment_comms[i]["action_item"] = action_items[i]["id"]
    #         insert_payment_comms(payment_comms)
        
    #     update_issue_status(issue_id, "in-progress")
    #     # Return the first payment link
    #     return links[0]
        
