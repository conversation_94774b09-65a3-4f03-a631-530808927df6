import os
import stripe
import time
from app.utils.supabase.queries import (
    insert_payment_duplicate,
    update_payment,
    get_payment_duplicate,
    get_payments_by_issue_id_duplicate,
    get_payments_by_issue_id,
    update_payment_link_status,
    get_issue_by_id,
    get_defaulter_by_id,
    get_payment_by_stripe_invoice_id,
    update_issue,
    get_issues_by_defaulter_id,
    delete_action_items,
    get_payment_by_stripe_subscription_id,
    get_sample_payments,
    get_payments_by_issue_id,
    insert_notification
)
from app.utils.supabase.queries import get_issue_by_id, get_defaulter_by_id
from datetime import datetime
import re
from datetime import time as dt_time


def parse_money_to_cents(text: str) -> int:
    """
    Converts a string like "$1,234.56" to an integer value in cents: 123456
    """
    # Remove anything that's not a digit or dot
    clean = re.sub(r'[^\d.]', '', text)
    
    # Handle decimal point if it exists
    if '.' in clean:
        dollars, cents = clean.split('.')
        cents = (cents + '00')[:2]  # pad/truncate to 2 digits
    else:
        dollars, cents = clean, '00'
    
    return int(dollars) * 100 + int(cents)

stripe.api_key = os.getenv("STRIPE_API_KEY")


def generate_payment_description(issue_id, amount_cents, recurring=False, interval=None, interval_count=None):
    """
    Generate descriptive text for invoice line items that includes payment plan details.
    """
    # Ensure amount_cents is numeric
    amount_cents = int(amount_cents or 0)
    
    if not recurring:
        return f"One-time Payment for Issue {issue_id} - ${amount_cents/100:.2f}"
    
    # For recurring payments, include frequency information
    if interval == "week" and interval_count == 2:
        frequency = "Biweekly"
    elif interval == "week" and interval_count == 1:
        frequency = "Weekly"
    elif interval == "month" and interval_count == 1:
        frequency = "Monthly"
    else:
        frequency = f"Every {interval_count} {interval}(s)"
    
    return f"{frequency} Payment Plan for Issue {issue_id} - ${amount_cents/100:.2f}"


def generate_comprehensive_footer(issue, amount_cents, recurring=False, interval=None, interval_count=None):
    """
    Generate a comprehensive footer with payment structure information.
    """
    try:
        issue_id = issue["issue_id"]
        # Ensure numeric values (database might return strings)
        # Handle currency strings like "$600.13" by parsing them properly
        outstanding_amount_str = issue.get("outstanding_amount", "0")
        if isinstance(outstanding_amount_str, str) and outstanding_amount_str.startswith('$'):
            # Use the existing parse_money_to_cents function
            outstanding_amount = parse_money_to_cents(outstanding_amount_str)
        else:
            outstanding_amount = int(outstanding_amount_str or 0)
        
        original_amount_str = issue.get("original_amount", outstanding_amount_str)
        if isinstance(original_amount_str, str) and original_amount_str.startswith('$'):
            original_amount = parse_money_to_cents(original_amount_str)
        else:
            original_amount = int(original_amount_str or outstanding_amount)
        
        amount_cents = int(amount_cents or 0)
        
        # Get payment information
        payments = get_payments_by_issue_id(issue_id).data
        paid_payments = [p for p in payments if p.get("status") == "paid"]
        pending_payments = [p for p in payments if p.get("status") not in ["paid", "cancelled", "failed"]]
        
        # Ensure payment amounts are numeric (convert strings to int)
        paid_amount = sum(int(p.get("amount", 0) or 0) for p in paid_payments)
        emis_left = len(pending_payments)
        
        footer_lines = []
        
        if recurring:
            # For recurring payments, show comprehensive plan information
            if interval == "week" and interval_count == 2:
                plan_type = "Biweekly"
                frequency_text = "every 2 weeks"
            elif interval == "week" and interval_count == 1:
                plan_type = "Weekly"
                frequency_text = "every week"
            elif interval == "month" and interval_count == 1:
                plan_type = "Monthly"
                frequency_text = "every month"
            else:
                plan_type = f"Every {interval_count} {interval}(s)"
                frequency_text = f"every {interval_count} {interval}(s)"
            
            footer_lines.append("RECURRING PAYMENT PLAN")
            footer_lines.append(f"Plan Type: {plan_type} Payments")
            footer_lines.append(f"Payment Amount: ${amount_cents/100:.2f} {frequency_text}")
            footer_lines.append(f"Outstanding Balance: ${outstanding_amount/100:.2f}")
            footer_lines.append(f"EMIs Remaining: {emis_left}")
            
            # Calculate estimated completion
            if amount_cents > 0:
                remaining_balance = outstanding_amount - paid_amount
                estimated_payments = max(1, int(remaining_balance / amount_cents))
                footer_lines.append(f"Est. Payments to Complete: {estimated_payments}")
                
                # Add time estimate
                if interval == "week":
                    weeks = estimated_payments * interval_count
                    if weeks < 4:
                        footer_lines.append(f"Est. Completion: {weeks} weeks")
                    else:
                        months = weeks / 4.33
                        footer_lines.append(f"Est. Completion: {months:.1f} months")
                elif interval == "month":
                    months = estimated_payments * interval_count
                    if months < 12:
                        footer_lines.append(f"Est. Completion: {months} months")
                    else:
                        years = months / 12
                        footer_lines.append(f"Est. Completion: {years:.1f} years")
            
            footer_lines.append("")
            footer_lines.append("Payments will recur automatically.")
            footer_lines.append("Contact support for questions or changes.")
            
        else:
            # For one-time payments, show simpler information
            footer_lines.append("ONE-TIME PAYMENT")
            footer_lines.append(f"Payment Amount: ${amount_cents/100:.2f}")
            footer_lines.append(f"Outstanding Balance: ${outstanding_amount/100:.2f}")
            footer_lines.append(f"EMIs Remaining: {emis_left}")
            
            if amount_cents >= outstanding_amount:
                footer_lines.append("This payment will satisfy your full outstanding balance.")
            else:
                remaining_after = outstanding_amount - amount_cents
                footer_lines.append(f"Remaining after payment: ${remaining_after/100:.2f}")
            
            footer_lines.append("")
            footer_lines.append("Thank you for your payment.")
        
        # Add payment history if available
        if paid_payments:
            footer_lines.append("")
            footer_lines.append(f"Previous Payments: {len(paid_payments)} completed")
            footer_lines.append(f"Total Paid: ${paid_amount/100:.2f}")
        
        footer_lines.append("")
        footer_lines.append("Questions? Contact our support team.")
        
        return "\n".join(footer_lines)
        
    except Exception as e:
        # Fallback to simple footer with safe type conversion
        try:
            outstanding_amount_str = issue.get("outstanding_amount", "0")
            if isinstance(outstanding_amount_str, str) and outstanding_amount_str.startswith('$'):
                outstanding_amount = parse_money_to_cents(outstanding_amount_str)
            else:
                outstanding_amount = int(outstanding_amount_str or 0)
            amount_cents = int(amount_cents or 0)
            return f"Outstanding balance: ${outstanding_amount/100:.2f} | Payment: ${amount_cents/100:.2f}"
        except (ValueError, TypeError):
            return f"Payment for Issue {issue.get('issue_id', 'Unknown')}"


def create_payment_link(issue_id, amount_cents, recurring=False, start_date=None):
    """
    Create a Stripe Invoice for a given issue and amount, store in payment_links_duplicate.
    Stripe will automatically email the invoice to the customer.
    No custom email logic is used.
    """
    # Fetch customer info from DB
    issue = get_issue_by_id(issue_id).data[0]
    defaulter_id = issue["defaulter_id"]
    defaulter = get_defaulter_by_id(defaulter_id).data[0]
    customer_email = defaulter["email"]
    customer_name = defaulter["name"]

    # Ensure amount_cents is nonzero
    if amount_cents is None or amount_cents <= 0:
        raise ValueError("Invoice amount must be greater than zero.")

    # Create or retrieve Stripe customer
    customers = stripe.Customer.list(email=customer_email, limit=1)
    if customers.data:
        customer = customers.data[0]
    else:
        customer = stripe.Customer.create(email=customer_email, name=customer_name)

    # Generate footer and metadata BEFORE creating invoice
    try:
        footer = generate_comprehensive_footer(issue, amount_cents, recurring)
        metadata = {
            "issue_id": str(issue_id),
            "payment_type": "recurring" if recurring else "one_time",
            "payment_amount": str(amount_cents),
            "outstanding_amount": str(issue.get("outstanding_amount", 0)),
            "original_amount": str(issue.get("original_amount", 0))
        }
    except Exception as e:
        print(f"Warning: Could not generate footer and metadata: {e}")
        footer = f"Payment for Issue {issue_id} - ${amount_cents/100:.2f}"
        metadata = {"issue_id": str(issue_id)}

    # 1. Create the invoice (in draft mode) with footer and metadata
    invoice = stripe.Invoice.create(
        customer=customer.id,
        collection_method='send_invoice',
        days_until_due=7,
        auto_advance=True,
        footer=footer,
        metadata=metadata
    )

    # 2. Create the invoice item with enhanced description
    description = generate_payment_description(issue_id, amount_cents, recurring)
    stripe.InvoiceItem.create(
        customer=customer.id,
        amount=amount_cents,
        currency="usd",
        description=description,
        invoice=invoice.id  # Attach to the invoice
    )

    # Finalize the invoice to get the hosted_invoice_url
    invoice = stripe.Invoice.finalize_invoice(invoice.id)
    invoice = stripe.Invoice.retrieve(invoice.id)

    # 3. Send the invoice (Stripe will email the customer)
    stripe.Invoice.send_invoice(invoice.id)

    # Insert into payment_links for tracking
    db_result = insert_payment_duplicate(
        amount=amount_cents,
        issue_id=issue_id,
        recurring=recurring,
        approved=False,
        settlement_discount=False,
        stripe_invoice_id=invoice.id,
        url=invoice.hosted_invoice_url,
        type="recurring" if recurring else "one_time",
        due_date=None,  # For create_payment_link, we use default Stripe timing
        start_date=None,  # For create_payment_link, start_date not used
    )
    return invoice.hosted_invoice_url


def process_webhook(event):
    """
    Process Stripe webhook events for invoice payment success/failure.
    """
    try:
        event_type = event["type"]
        data = event["data"]["object"]
        invoice_id = data.get("id")

        if not invoice_id:
            return

        # Skip processing for $0 trial invoices that are automatically marked as paid
        if event_type == "invoice.payment_succeeded" and data.get("total", 0) == 0:
            return

        # Map of event types to statuses
        status_map = {
            "invoice.payment_succeeded": "paid",
            "invoice.payment_failed": "failed",
            "invoice.marked_uncollectible": "uncollectible",
            "invoice.voided": "voided",
            "invoice.payment_action_required": "action_required",
            "invoice.overdue": "overdue"
        }

        if event_type in status_map:
            status = status_map[event_type]
            update_data = {
                "status": status,
                "stripe_status": data["status"],
                "last_successful_payment": datetime.utcnow().date() if status == "paid" else None
            }

            # Add payment intent ID if available
            if payment_intent_id := data.get("payment_intent"):
                update_data["stripe_payment_intent_id"] = payment_intent_id

            # Add failure reason if available
            if failure_reason := data.get("last_payment_error", {}).get("message"):
                update_data["failure_reason"] = failure_reason

            update_payment(invoice_id, **update_data)

            # If payment succeeded, update the outstanding balance
            if status == "paid":
                try:
                    # Get the payment record to find the issue and amount
                    payment_result = get_payment_by_stripe_invoice_id(invoice_id)
                    
                    if payment_result.data and len(payment_result.data) > 0:
                        payment = payment_result.data[0]
                        issue_id = payment.get("issue_id")
                        payment_amount = int(payment.get("amount", 0))
                        
                        if issue_id and payment_amount > 0:
                            # Get the current issue
                            issue = get_issue_by_id(issue_id).data[0]
                            current_outstanding_str = issue.get("outstanding_amount", "$0.00")
                            
                            # Parse current outstanding amount (handle both string and int formats)
                            if isinstance(current_outstanding_str, str):
                                # Remove $ and , characters, then convert to cents
                                clean_amount = re.sub(r'[^\d.]', '', current_outstanding_str)
                                if '.' in clean_amount:
                                    dollars, cents = clean_amount.split('.')
                                    cents = (cents + '00')[:2]  # pad/truncate to 2 digits
                                    current_outstanding_cents = int(dollars) * 100 + int(cents)
                                else:
                                    current_outstanding_cents = int(clean_amount) * 100 if clean_amount else 0
                            else:
                                current_outstanding_cents = int(current_outstanding_str or 0)
                            
                            # Calculate new outstanding balance
                            new_outstanding_cents = max(0, current_outstanding_cents - payment_amount)
                            
                            # Format back to currency string
                            new_outstanding_dollars = new_outstanding_cents // 100
                            new_outstanding_cents_part = new_outstanding_cents % 100
                            new_outstanding_str = f"${new_outstanding_dollars:,}.{new_outstanding_cents_part:02d}"
                            
                            # Update the issue
                            update_data = {"outstanding_amount": new_outstanding_str}
                            
                            # If balance is now zero, mark as solved
                            if new_outstanding_cents == 0:
                                update_data["status"] = "solved"
                            
                            update_issue(issue_id, update_data)
                            
                            # If all issues for this defaulter are solved, clean up action items
                            if new_outstanding_cents == 0:
                                defaulter_id = issue.get("defaulter_id")
                                if defaulter_id:
                                    all_issues = get_issues_by_defaulter_id(defaulter_id).data
                                    all_solved = all(iss.get("status") == "solved" for iss in all_issues)
                                    if all_solved:
                                        delete_action_items(defaulter_id)
                    
                except Exception as e:
                    pass

            # If payment succeeded, check if this was part of a subscription
            if status == "paid" and (subscription_id := data.get("subscription")):
                try:
                    subscription = stripe.Subscription.retrieve(subscription_id)
                    # Update the payment record with subscription details
                    update_data = {
                        "stripe_subscription_id": subscription_id,
                        "next_payment_date": datetime.fromtimestamp(subscription.current_period_end).date()
                    }
                    update_payment(invoice_id, **update_data)
                except Exception as e:
                    pass

            # Handle overdue invoices specifically
            elif status == "overdue":
                # Optionally, notify the user/defaulter
                payment_result = get_payment_by_stripe_invoice_id(invoice_id)
                if payment_result.data and len(payment_result.data) > 0:
                    payment = payment_result.data[0]
                    issue_id = payment.get("issue_id")
                    if issue_id:
                        try:
                            issue = get_issue_by_id(issue_id).data[0]
                            defaulter_id = issue.get("defaulter_id")
                            if defaulter_id:
                                insert_notification(defaulter_id, f"The invoice {invoice_id} is overdue.")
                        except Exception as e:
                            print(f"Warning: Could not send overdue notification: {e}")

        elif event_type == "customer.subscription.deleted":
            # Handle subscription cancellation
            subscription_id = data.get("id")
            if subscription_id:
                # Find the payment by subscription_id
                result = get_payment_by_stripe_subscription_id(subscription_id)
                if result.data and len(result.data) > 0:
                    payment_id = result.data[0]["id"]
                    update_payment(payment_id, status="cancelled", stripe_status="cancelled")

        else:
            pass

    except Exception as e:
        raise  # Re-raise to return 500 to Stripe


def update_payment_duplicate_by_invoice(stripe_invoice_id, **kwargs):
    """
    Update payment_links by Stripe invoice ID.
    """
    # Find the payment by stripe_invoice_id
    # print(f"[DEBUG] Looking for payment with stripe_invoice_id: {stripe_invoice_id}")
    result = get_payment_by_stripe_invoice_id(stripe_invoice_id)
    # print(f"[DEBUG] Query result: {result.data}, count: {len(result.data) if result.data else 0}")
    
    if result.data and len(result.data) > 0:
        payment_id = result.data[0]["id"]
        update_payment(payment_id, **kwargs)
    else:
        # Let's try a broader search to see if the payment exists at all
        all_payments = get_sample_payments(5)


def create_invoice(issue_id, amount_cents, recurring=False, due_date=None, insert_db=True):
    """
    Create a Stripe Invoice for a given issue and amount, but DO NOT send it.
    Returns the invoice object (including hosted_invoice_url).
    """
    # Fetch customer info from DB
    issue = get_issue_by_id(issue_id).data[0]
    defaulter_id = issue["defaulter_id"]
    defaulter = get_defaulter_by_id(defaulter_id).data[0]
    customer_email = defaulter["email"]
    customer_name = defaulter["name"]

    # Ensure amount_cents is nonzero
    if amount_cents is None or amount_cents <= 0:
        raise ValueError("Invoice amount must be greater than zero.")

    try:
        # Create or retrieve Stripe customer
        customers = stripe.Customer.list(email=customer_email, limit=1)
        if customers.data:
            customer = customers.data[0]
        else:
            customer = stripe.Customer.create(email=customer_email, name=customer_name)

        # Generate footer and metadata BEFORE creating invoice
        try:
            footer = generate_comprehensive_footer(issue, amount_cents, recurring)
            metadata = {
                "issue_id": str(issue_id),
                "payment_type": "recurring" if recurring else "one_time",
                "payment_amount": str(amount_cents),
                "outstanding_amount": str(issue.get("outstanding_amount", 0)),
                "original_amount": str(issue.get("original_amount", 0))
            }
        except Exception as e:
            footer = f"Payment for Issue {issue_id} - ${amount_cents/100:.2f}"
            metadata = {"issue_id": str(issue_id)}

        # For recurring payments, ignore due_date
        parsed_due_date = None
        if not recurring and due_date:
            if isinstance(due_date, str):
                try:
                    parsed_due_date = datetime.strptime(due_date, "%Y-%m-%d").date()
                except ValueError as e:
                    parsed_due_date = None
            elif hasattr(due_date, 'strftime'):  # It's already a date/datetime object
                parsed_due_date = due_date
        
        # 1. Create the invoice (in draft mode) with footer and metadata
        invoice = stripe.Invoice.create(
            customer=customer.id,
            collection_method='send_invoice',
            days_until_due=7 if not parsed_due_date else None if not recurring else 7,
            due_date=int(datetime.combine(parsed_due_date, dt_time(23, 59, 59)).timestamp()) if parsed_due_date and not recurring else None,
            auto_advance=True,
            footer=footer,
            metadata=metadata
        )

        # 2. Create the invoice item with enhanced description
        description = generate_payment_description(issue_id, amount_cents, recurring)
        stripe.InvoiceItem.create(
            customer=customer.id,
            amount=amount_cents,
            currency="usd",
            description=description,
            invoice=invoice.id  # Attach to the invoice
        )

        # Finalize the invoice to get the hosted_invoice_url
        invoice = stripe.Invoice.finalize_invoice(invoice.id)
        
        # Retry logic for retrieving the invoice with hosted_invoice_url
        max_retries = 5
        retry_delay = 2  # seconds
        for attempt in range(max_retries):
            invoice = stripe.Invoice.retrieve(invoice.id)
            if invoice.hosted_invoice_url:
                break
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                continue
            raise Exception("Failed to get hosted_invoice_url after maximum retries")

        if insert_db:
            # Insert into payment_links for tracking
            # Convert date object to string format if due_date is provided
            due_date_str = parsed_due_date.strftime("%Y-%m-%d") if parsed_due_date and not recurring else None
            db_result = insert_payment_duplicate(
                amount=amount_cents,
                issue_id=issue_id,
                recurring=recurring,
                approved=False,
                settlement_discount=False,
                stripe_invoice_id=invoice.id,
                url=invoice.hosted_invoice_url,
                type="recurring" if recurring else "one_time",
                due_date=due_date_str,
                start_date=None,  # For create_invoice, start_date not typically used
            )
        return invoice

    except stripe.error.StripeError as e:
        raise
    except Exception as e:
        raise


def create_stripe_subscription(issue_id, amount_cents, interval="month", interval_count=1, start_date=None):
    """
    Create a Stripe Subscription for a given issue and amount.
    Returns the subscription object.
    interval: 'month', 'week', etc.
    interval_count: 1 for monthly, 2 for biweekly, etc.
    """
    # Fetch customer info from DB
    issue = get_issue_by_id(issue_id).data[0]
    defaulter_id = issue["defaulter_id"]
    defaulter = get_defaulter_by_id(defaulter_id).data[0]
    customer_email = defaulter["email"]
    customer_name = defaulter["name"]

    # Create or retrieve Stripe customer
    customers = stripe.Customer.list(email=customer_email, limit=1)
    if customers.data:
        customer = customers.data[0]
    else:
        customer = stripe.Customer.create(email=customer_email, name=customer_name)

    # Determine plan type for naming
    if interval == "week" and interval_count == 2:
        plan_type = "Biweekly"
    elif interval == "week" and interval_count == 1:
        plan_type = "Weekly"
    elif interval == "month" and interval_count == 1:
        plan_type = "Monthly"
    else:
        plan_type = f"Every {interval_count} {interval}(s)"

    product_name = f"{plan_type} Payment Plan for Issue {issue_id}"
    product_description = f"{plan_type} payment for your outstanding balance on Issue {issue_id}."
    price_nickname = f"{plan_type} Payment"

    product = stripe.Product.create(name=product_name, description=product_description)

    # Create a price for the subscription with a nickname
    price = stripe.Price.create(
        unit_amount=amount_cents,
        currency="usd",
        recurring={"interval": interval, "interval_count": interval_count},
        product=product.id,
        nickname=price_nickname
    )

    # Create the subscription with optional start date
    subscription_params = {
        "customer": customer.id,
        "items": [{"price": price.id}],
        "payment_behavior": "default_incomplete",  # Invoice will be created, payment link can be sent
        "expand": ["latest_invoice.payment_intent"],
    }
    
    # Parse start_date if it's provided and is a string
    parsed_start_date = None
    if start_date:
        if isinstance(start_date, str):
            try:
                parsed_start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
                print(f"[DEBUG] Parsed start_date string '{start_date}' to date object: {parsed_start_date}")
            except ValueError as e:
                print(f"[ERROR] Invalid start_date format '{start_date}': {e}")
                parsed_start_date = datetime.now().date()
        elif hasattr(start_date, 'strftime'):  # It's already a date/datetime object
            parsed_start_date = start_date
            print(f"[DEBUG] Using start_date as date object: {parsed_start_date}")
    
    # If start_date is provided, delay billing until that date
    # if parsed_start_date:
        # Check if the start date is today or in the past
        # today = datetime.now().date()
        # if parsed_start_date <= today:
            # If start date is today or in the past, start immediately (no trial period)
            # print(f"Start date {parsed_start_date} is today or in the past, starting subscription immediately")
            # Don't set trial_end - let Stripe create the first invoice immediately
        
        # else:
        #     # If start date is in the future, set trial end to the end of that day
        #     trial_end_datetime = datetime.combine(parsed_start_date, datetime.max.time())
        #     subscription_params["trial_end"] = int(trial_end_datetime.timestamp())
        #     print(f"Subscription will start billing on: {parsed_start_date}")

    subscription = stripe.Subscription.create(**subscription_params)

    # Add comprehensive footer and metadata to the first invoice BEFORE finalizing
    invoice_id = subscription.get("latest_invoice")
    if invoice_id:
        try:
            # Retrieve the invoice first
            invoice = stripe.Invoice.retrieve(invoice_id)
            
            # Only modify if it's still in draft status
            if invoice.status == 'draft':
                footer = generate_comprehensive_footer(issue, amount_cents, True, interval, interval_count)
                stripe.Invoice.modify(
                    invoice_id,
                    footer=footer,
                    metadata={
                        "issue_id": str(issue_id),
                        "payment_type": "recurring",
                        "plan_type": plan_type,
                        "interval": interval,
                        "interval_count": str(interval_count),
                        "payment_amount": str(amount_cents),
                        "outstanding_amount": str(issue.get("outstanding_amount", 0)),
                        "original_amount": str(issue.get("original_amount", 0)),
                        "subscription_id": subscription.id
                    }
                )
            else:
                # Only log as debug since this is expected behavior
                print(f"[DEBUG] Invoice {invoice_id} is already finalized (status: {invoice.status}), skipping footer modification")
        except Exception as e:
            # Only log as debug since this is not critical
            print(f"[DEBUG] Could not set invoice footer and metadata: {e}")

    # NOTE: Don't insert a new payment record here since one already exists
    # The existing payment record will be updated with the stripe_invoice_id
    # in the send_payment_link function
    return subscription 