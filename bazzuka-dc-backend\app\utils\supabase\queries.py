from .client import supabase
from datetime import datetime, timedelta
import time
import httpx
from httpcore import RemoteProtocolError
import os
import uuid


def execute_with_retry(query_func, max_retries=3, retry_delay=1):
    """
    Execute a Supabase query with retry logic for connection errors.

    Args:
        query_func: A function that returns a Supabase query to execute
        max_retries: Maximum number of retry attempts
        retry_delay: Base delay between retries in seconds

    Returns:
        The result of the query execution
    """
    for attempt in range(max_retries):
        try:
            return query_func()
        except (httpx.RemoteProtocolError, RemoteProtocolError, httpx.ConnectError, httpx.TimeoutException) as e:
            # print(f"Supabase connection error on attempt {attempt + 1}: {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay * (attempt + 1))  # Exponential backoff
                continue
            else:
                # print(f"Failed to connect to Supabase after {max_retries} attempts")
                raise e
        except Exception as e:
            # print(f"Unexpected error in Supabase query: {e}")
            raise e


def insert_comm_logs(
    defaulter_id: str,
    channel: str,
    comm_id: int,
    direction: str,
    timestamp: str,
    summary: str,
    payment_likelihood: int=None,
):
    return (
        supabase.table("commlogs")
        .insert(
            {
                "defaulter_id": defaulter_id,
                "channel": channel,
                "comm_id": comm_id,
                "direction": direction,
                "timestamp": timestamp,
                "summary": summary,
                "payment_likelihood": payment_likelihood,
            }
        )
        .execute()
    )


def get_comm_logs_by_defaulter_id(defaulter_id: str):
    return supabase.table("commlogs").select("*").eq("defaulter_id", defaulter_id).execute()

def get_comm_logs(
    defaulter_id: str = None, org_id: str = None, limit: int = None, offset: int = 0
) -> list:
    # TODO: implement time range from from_date to to_date
    request = supabase.table("commlogs")

    if org_id:
        request = request.select("*, issues!inner(org_id)").eq(
            "defaulters.org_id", org_id
        )
    else:
        request = request.select("*")

    if defaulter_id is not None:
        request = request.eq("defaulter_id", defaulter_id)

    if limit is not None:
        request = request.range(offset, offset + limit - 1)

    request = request.order("timestamp", desc=True)

    response = request.execute()
    return response.data if response.data else []


def upsert_issue(defaulter_id: int, status: str) -> bool:
    return (
        supabase.table("issues")
        .upsert({"defaulter_id": defaulter_id, "status": status})
        .execute()
    )


def insert_issue(data: dict):
    return supabase.table("issues").insert(data).execute()


def insert_issues_bulk(issues_data: list):
    """Insert multiple issues in a single batch operation"""
    return supabase.table("issues").insert(issues_data).execute()


def insert_email(
    timestamp: str,
    message_id: str,
    defaulter_id: str,
    email_subject: str,
    email_body: str,
    direction: str,
):
    return (
        supabase.table("emaillogs")
        .insert(
            {
                "timestamp": timestamp,
                "message_id": message_id,
                "defaulter_id": defaulter_id,
                "email_subject": email_subject,
                "email_body": email_body,
                "direction": direction,
            }
        )
        .execute()
    )


def get_summaries_by_defaulter_id(defaulter_id: str) -> str | None:
    response = (
        supabase.table("summaries")
        .select("summary")
        .eq("defaulter_id", defaulter_id)
        .execute()
    )
    return response


# TODO: updates should be like upsert
def upsert_summary(defaulter_id: str, summary: str) -> bool:
    return (
        supabase.table("summaries")
        .upsert(
            {"defaulter_id": defaulter_id, "summary": summary},
            on_conflict=["defaulter_id"],
        )
        .execute()
    )


def insert_summary(defaulter_id: str, summary: str, comm_id: str = None) -> bool:
    return (
        supabase.table("summaries")
        .insert({"defaulter_id": defaulter_id, "summary": summary, "comm_id": comm_id})
        .execute()
    )


def insert_action_item(
    action_date: str,
    action_time: str,
    action_channel: str,
    action_channel_content: str,
    defaulter_id: str,
    action_reason: str,
    payment_likelihood: int,
    category: str,
    _approved: bool = True,
    is_human_followup: bool = False,
) -> bool:
    # Create insert data with exact field names matching the database schema
    insert_data = {
        "action_date": action_date,
        "action_time": action_time,
        "action_channel": action_channel,
        "defaulter_id": defaulter_id,
        "action_reason": action_reason,
        "payment_likelihood": payment_likelihood,
        "category": category,
        "_approved": _approved,
        "timestamp": datetime.now().isoformat(),
        "is_human_followup": is_human_followup,
    }

    try:
        result = supabase.table("actionitems").insert(insert_data).execute()
        # Insert notification for follow-up
        if is_human_followup:
            if result and getattr(result, 'data', None):
                # from app.utils.supabase.queries import insert_notification
                defaulter = get_defaulter_by_id(defaulter_id).data[0]

                insert_notification(
                    defaulter_id,
                    f"Follow-up with {defaulter['name']} ({defaulter['phone']}) on {action_date} at {action_time} to discuss {action_reason}",
                    is_read=False,
                    is_done=False,
                    is_canceled=False
                )
        return result
    except Exception as e:
        # print(f"Insert error: {str(e)}")
        # If that fails, try with a more minimal set of fields
        minimal_data = {
            "action_date": action_date,
            "action_time": action_time,
            "action_channel": action_channel,
            "defaulter_id": defaulter_id,
            "action_reason": action_reason,
            "timestamp": datetime.now().isoformat(),
            "is_human_followup": is_human_followup,
        }

        try:
            result = supabase.table("actionitems").insert(minimal_data).execute()
            # Insert notification for follow-up (minimal)
            if is_human_followup:
                if result and getattr(result, 'data', None):
                    # from app.utils.supabase.queries import insert_notification
                    defaulter = get_defaulter_by_id(defaulter_id).data[0]

                    insert_notification(
                        defaulter_id,
                        f"Follow-up with {defaulter['name']} ({defaulter['phone']}) on {action_date} at {action_time} to discuss {action_reason}",
                        is_read=False,
                        is_done=False,
                        is_canceled=False
                    )
            return result
        except Exception as e2:
            # print(f"Second insert error: {str(e2)}")
            raise Exception(f"Failed to insert action item: {str(e2)}")


def delete_action_item(action_item_id: int) -> bool:
    return supabase.table("actionitems").delete().eq("id", action_item_id).execute()


def insert_email_comm_combined(
    timestamp: str,
    message_id: str,
    defaulter_id: str,
    email_subject: str,
    email_body: str,
    direction: str,
    summary: str,
    payment_likelihood: int=None,
) -> dict:
    eml_response = insert_email(
        timestamp, message_id, defaulter_id, email_subject, email_body, direction
    )
    email_id = eml_response.data[0]["id"]
    comm_response = insert_comm_logs(
        defaulter_id, "email", email_id, direction, timestamp, summary, payment_likelihood
    )

    return {
        "emaillogs": {
            "data": eml_response.data,
        },
        "commlogs": {
            "data": comm_response.data,
        },
    }


def get_emails_by_id(email_ids: list) -> list:
    # print(email_ids)
    response = supabase.table("emaillogs").select("*").in_("id", email_ids).execute()
    return response


def get_emails_by_message_id(message_id: str) -> list:
    response = (
        supabase.table("emaillogs").select("*").eq("message_id", message_id).execute()
    )
    return response


def get_issue_by_defaulter_id(defaulter_id: int) -> str | None:
    response = (
        supabase.table("issues")
        .select("defaulter_id")
        .eq("defaulter_id", defaulter_id)
        .execute()
    )
    return response


def get_action_items_by_org_id(org_id: str, limit: int, offset: int) -> list:
    # instead of where _approved = true, where defaulters.paused = false
    response = (
        supabase.table("actionitems")
        .select("*, defaulters!inner(*)")  # Join with both defaulters
        .order("action_date", desc=False)
        .order("action_time", desc=False)
        .range(offset, offset + limit)
        .eq("defaulters.paused", False)
        .execute()
    )
    return response


def get_action_items_by_date_and_time(action_date: str, action_time: str) -> list:
    response = (
        supabase.table("actionitems")
        .select("*")
        .eq("action_date", action_date)
        .eq("action_time", action_time)
        .execute()
    )
    return response


def get_seven_day_call_count():
    seven_days_ago = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
    response = (
        supabase.table("commlogs")
        .select("*", count="exact")
        .eq("channel", "call")
        .gte("timestamp", seven_days_ago)
        .execute()
    )
    return response.count


def old_get_comm_stats(org_id: str):
    """
    rpc ======================================================
    create or replace function get_seven_day_activity_counts(input_org_id uuid)
    returns jsonb as $$
    begin
    return (
        select jsonb_agg(result)
        from (
        select
            channel,
            count(*) as count
        from commlogs
        join issues on commlogs.defaulter_id = issues.defaulter_id
        where issues.org_id = input_org_id
            and commlogs.timestamp >= now() - interval '7 days'
            and channel in ('call', 'email')
        group by channel
        ) as result
    );
    end;
    $$ language plpgsql;
    ================================================== end rpc
    TODO: Make sure channel column is indexed!
    """
    response = supabase.rpc(
        "get_seven_day_activity_counts", {"input_org_id": org_id}
    ).execute()
    result = {"calls": 0, "emails": 0}
    for item in response.data:
        result[item["channel"] + "s"] = item["count"]
    return result


def get_org_by_id(org_id: str):
    response = supabase.table("organizations").select("*").eq("id", org_id).execute()
    return response


def upsert_org_metadata(org_id: str, metadata: dict):
    return (
        supabase.table("organizations")
        .upsert({"id": org_id, "metadata": metadata}, on_conflict=["id"])
        .execute()
    )


def get_action_item_by_id(id: int, org_id: str):
    # TODO: this can probably be rewritten after RLS is setup
    return (
        supabase.table("actionitems")
        .select("*")
        .select("*, issues!inner(org_id)")
        .eq("id", id)
        .eq("org_id", org_id)
        .execute()
    )


def update_action_item(id: int, updates: dict):
    accepted_fields = [
        "action_date",
        "action_time",
        "action_channel",  # Use action_channel instead of action_channel_id
        "action_reason"
        # Removed action_channel_content
    ]

    # Filter updates to only include accepted fields
    filtered_updates = {k: v for k, v in updates.items() if k in accepted_fields}

    # Handle the case where action_channel_id is provided instead of action_channel
    if "action_channel_id" in updates and "action_channel" not in filtered_updates:
        filtered_updates["action_channel"] = updates["action_channel_id"]

    # Remove action_channel_id if present in updates
    if "action_channel_id" in filtered_updates:
        del filtered_updates["action_channel_id"]

    # Remove action_channel_content if present in updates
    if "action_channel_content" in filtered_updates:
        del filtered_updates["action_channel_content"]

    if not filtered_updates:
        return None

    # Ensure action_date is in the correct format if present
    if "action_date" in filtered_updates:
        try:
            # Validate the date format
            datetime.strptime(filtered_updates["action_date"], "%Y-%m-%d")
        except ValueError:
            # If date is invalid, remove it from updates
            del filtered_updates["action_date"]

    # Ensure action_time is in the correct format if present
    if "action_time" in filtered_updates:
        try:
            # Validate the time format
            datetime.strptime(filtered_updates["action_time"], "%H:%M:%S")
        except ValueError:
            # If time is invalid, remove it from updates
            del filtered_updates["action_time"]

    try:
        return supabase.table("actionitems").update(filtered_updates).eq("id", id).execute()
    except Exception as e:
        # print(f"Update error: {str(e)}")
        raise e


def delete_action_item_by_id(id: int):
    return (
        supabase.table("actionitems")
        .delete()
        .eq("id", id)
        # .eq("org_id", org_id)
        .execute()
    )


def get_action_items_by_defaulter_id(defaulter_id: str):
    return (
        supabase.table("actionitems")
        .select("*")
        .eq("defaulter_id", defaulter_id)
        .execute()
    )


def insert_defaulters(data: dict):
    return supabase.table("defaulters").insert(data).execute()


def get_issues_by_email(email_address):
    # Get issue.defaulter_id where issue.defaulter_id = defaulter.id and defaulter.email = email_address
    return (
        supabase.table("defaulters")
        .select("*, issues!inner(*)")
        .eq("email", email_address)
        .execute()
    )


def get_issues_by_phone(phone_number):
    # Get issues.defaulter_id where issues.defaulter_id = defaulters.id and defaulters.email = email_address
    return (
        supabase.table("defaulters")
        .select("*, issues!inner(*)")
        .eq("phone", phone_number)
        .execute()
    )


def get_defaulter_by_email(email_address):
    return supabase.table("defaulters").select("*").eq("email", email_address).execute()


def get_defaulter_by_phone(phone_number):
    return supabase.table("defaulters").select("*").eq("phone", phone_number).execute()


def get_defaulter_by_id(defaulter_id):
    return execute_with_retry(
        lambda: supabase.table("defaulters").select("*").eq("id", defaulter_id).execute()
    )


def get_comm_by_id(comm_id):
    return supabase.table("commlogs").select("*").eq("comm_id", comm_id).execute()


def get_most_recent_action_item_for_issue(defaulter_id: str):
    # Get most recent action item for issue ID
    return (
        supabase.table("actionitems")
        .select("*")
        .eq("defaulter_id", defaulter_id)
        .order("action_date", desc=True)
        .order("action_time", desc=True)
        .limit(1)
        .execute()
    )


def get_defaulters_by_ids(defaulter_ids):
    return (
        supabase.table("issues")
        .select("defaulter_id, defaulters!inner(*)")  # Include defaulter_id
        .in_("defaulter_id", defaulter_ids)
        .execute()
    )


def get_defaulter_for_issue(issue_id):
    return (
        supabase.table("issues")
        .select("defaulter_id, defaulters!inner(*)")  # Include defaulter_id
        .eq("id", issue_id)
        .execute()
    )


def get_org():
    # get_org_id
    org_id = "8241d390-a8b5-4f59-b0a9-b95c074db3f5"
    return supabase.table("organizations").select("*").eq("id", org_id).execute()


def get_payments_by_issue_id(issue_id: int):
    return supabase.table("payment_links").select("*").eq("issue_id", issue_id).execute()

def insert_payment(defaulter_id: int, amount: int, due_date: str, issue_id: int):
    return (
        supabase.table("payments")
        .insert(
            {
                "defaulter_id": defaulter_id,
                "amount": amount,
                "due_date": due_date,
                "status": "pending",
                "issue_id": issue_id,
            }
        )
        .execute()
    )


def insert_payments(payments: list):
    return (
        supabase.table("payments")
        .insert(payments)
        .execute()
    )

def insert_action_items(action_items: list):
    return (
        supabase.table("actionitems")
        .insert(action_items, returning="minimal")
        .execute()
    )


def get_payment(payment_id):
    # Always use payment_links table since that's where all payments should be stored
    # Note: If you're using payment_links_duplicate, you may need to update this
    return execute_with_retry(
        lambda: supabase.table("payment_links").select("*").eq("id", payment_id).execute()
    )


def insert_payment_comms(payment_comms: list):
    return (
        supabase.table("payment_comms")
        .insert(payment_comms)
        .execute()
    )

def get_payment_plan_for_issue(issue_id):
    return supabase.table("payment_plans").select("*").eq("issue_id", issue_id).eq("status", "active").execute()

def deactivate_payment_plan(payment_plan_id):
    return (
        supabase.table("payment_plans")
        .update({"status": "inactive"})
        .eq("id", payment_plan_id)
        .execute()
    )

def insert_payment_plan(payment_plan):
    return (
        supabase.table("payment_plans")
        .insert(payment_plan)
        .execute()
    )

def delete_action_items(ids: list):
    return (
        supabase.table("actionitems")
        .delete()
        .in_("id", ids)
        .execute()
    )

def delete_payment_comms(ids: list):
    return (
        supabase.table("payment_comms")
        .delete()
        .in_("action_item", ids)
        .execute()
    )

def delete_action_items_by_defaulter(defaulter_id: str):
    return supabase.table("actionitems").delete().eq("defaulter_id", defaulter_id).execute()

def update_payments_status_deleted(payment_plan_id):
    # if payment status is unpaid and id is in payment_ids, set it to deleted
    return (
        supabase.table("payments")
        .update({"status": "deleted"})
        .eq("payment_plan", payment_plan_id)
        .eq("status", "unpaid")
        .execute()
    )

def get_action_items_for_payment_plan(payment_plan_id):
    return (
        supabase.table("payment_comms")
        .select("action_item")
        .eq("payment_plan", payment_plan_id)
        .execute()
    )


def set_payment_status(payment_id, status, transaction_id=None):
    return (
        supabase.table("payment_links")
        .update({"status": status, "transaction_id": transaction_id})
        .eq("id", payment_id)
        .execute()
    )

def update_comm_log(comm_log_id, updates):
    return (
        supabase.table("commlogs")
        .update(updates)
        .eq("id", comm_log_id)
        .execute()
    )

def insert_negotiation_strategy(defaulter_id, strategy):
    # Insert in defaulters.customizations : jsonb as {negotiation_strategy: strategy}
    return supabase.rpc(
        "update_customizations",
        {"defaulter_id": defaulter_id, "negotiation_strategy": strategy},
    )


def update_customizations(defaulter_id, updates):
    return supabase.rpc(
        "update_customizations", {"defaulter_id": defaulter_id, "updates": updates}
    ).execute()


def get_negotiation_strategy(defaulter_id):
    # Get defaulters.customizations.negotiation_strategy
    return (
        supabase.table("defaulters")
        .select("customizations->negotiation_strategy")
        .eq("id", defaulter_id)
        .execute()
    )


def delete_payment_reminders():
    # from actionitems where category = 'payment_reminder' and defaulter_id = defaulter_id
    return (
        supabase.table("actionitems")
        .delete()
        .eq("category", "payment_reminder")
        .execute()
    )


def get_call_forwarding_config(defaulter_id):
    # Fetch organization metadata.call_forwarding where issues.org_id = organizations.id
    # result = (
    #     supabase.table("issues")
    #     .select("organizations(metadata->>call_forwarding)")
    #     .eq("defaulter_id", defaulter_id)
    #     .execute()
    # )

    result = (
        supabase.table("issues")
        .select("organizations(metadata->>call_forwarding)")
        .eq("id", "8241d390-a8b5-4f59-b0a9-b95c074db3f5")
        .execute()
    )
    return result.data


def old_get_payments_collected():
    # Get sum of payments.amount where payments.status = 'collected'
    return supabase.rpc("get_payments_collected", {}).execute()


def get_issues_by_defaulter_id(defaulter_id):
    return (
        supabase.table("issues").select("*").eq("defaulter_id", defaulter_id).execute()
    )


def get_issue_by_id(issue_id):
    return execute_with_retry(
        lambda: supabase.table("issues").select("*").eq("issue_id", issue_id).execute()
    )


def get_payments_collected(start_date=None, end_date=None):
    # Query payments table directly to sum amounts where status is 'collected'
    query = supabase.table("payment_links") \
        .select("amount") \
        .eq("status", "paid")
    if start_date:
        query = query.gte("created_at", start_date)
    if end_date:
        query = query.lte("created_at", end_date)
    response = query.execute()

    # Initialize total_cents to 0
    total_cents = 0

    # Sum the amounts from the response data
    if response.data:
        total_cents = sum(payment.get("amount", 0) for payment in response.data)

    return total_cents


def update_most_recent_commlog_payment_likelihood(defaulter_id: str, payment_likelihood: int):
    """
    Update the payment likelihood for the most recent inbound commlog entry.

    Args:
        defaulter_id (str): The ID of the defaulter
        payment_likelihood (int): The payment likelihood value to set (0-5)
    """
    # First get the most recent inbound commlog
    result = supabase.table("commlogs") \
        .select("*") \
        .eq("defaulter_id", defaulter_id) \
        .eq("direction", "inbound") \
        .order("timestamp", desc=True) \
        .limit(1) \
        .execute()

    # Most recent inbound commlog

    if result.data and len(result.data) > 0:
        commlog = result.data[0]
        # Update the payment likelihood for this commlog
        return supabase.table("commlogs") \
            .update({"payment_likelihood": payment_likelihood}) \
            .eq("id", commlog["id"]) \
            .execute()
    return None

def get_yesterday_overdue_payments():
    yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    return supabase.table("payments").select("*").eq("due_date", yesterday).eq("status", "unpaid").execute()

def mark_payments_as_overdue(payment_ids: list):
    return (
        supabase.table("payments")
        .update({"status": "overdue"})
        .in_("id", payment_ids)
        .execute()
    )

def delete_action_item_for_payment(payment_id: int):
    # Get "payment_comms" where "payment_id" = payment_id
    # Delete the action item for each payment_comm
    # The action item id is payment_comm.action_item
    # The action items are in the "actionitems" table

    payment_comms = supabase.table("payment_comms").select("*").eq("payment_id", payment_id).execute()
    action_item_ids = [payment_comm["action_item"] for payment_comm in payment_comms.data]
    return supabase.table("actionitems").delete().in_("id", action_item_ids).execute()


def update_issue_status(issue_id: int, status: str):
    assert status in ["solved", "in-progress", "unsolved", "promise-failed"]

    return (
        supabase.table("issues")
        .update({"status": status})
        .eq("issue_id", issue_id)
        .execute()
    )

def get_upcoming_payments(from_date: str = None, to_date: str = None, limit: int = 8):
    # default from the beginning of time to a year from now
    if not from_date:
        from_date = "1970-01-01"
    if not to_date:
        to_date = (datetime.now() + timedelta(days=365)).strftime("%Y-%m-%d")

    # Get payments where status is active and due_date is within the range
    payments = (
        supabase.table("payments")
        .select("uuid, due_date, issue_id, amount")
        .eq("status", "active")
        .gte("due_date", from_date)
        .lte("due_date", to_date)
        .order("due_date", desc=False)  # Ascending order (closest to farthest)
        .limit(limit)
        .execute()
    )

    # Format the amount and create the URL
    payments.data = [
        {
            "url": f"https://sscd05m3-5000.use.devtunnels.ms/v0/{payment['uuid']}",
            "due_date": payment['due_date'],
            "issue_id": payment['issue_id'],
            "amount": f"${payment['amount'] / 100:,.2f}"
        }
        for payment in payments.data
    ]
    return payments.data

def insert_payment(amount: int, issue_id: int, recurring: bool = False, approved: bool = False, settlement_discount: bool = False):
    return (
        supabase.table("payment_links")
        .insert({
            "amount": amount,
            "issue_id": issue_id,
            "recurring": recurring,
            "_approved": "pending" if not approved else "approved",
            "settlement_discount": settlement_discount
        })
        .execute()
    )

def update_issue(issue_id: int, updates: dict):
    return (
        supabase.table("issues")
        .update(updates)
        .eq("issue_id", issue_id)
        .execute()
    )

def get_customer_requested_comms(defaulter_id: str):
    return supabase.table("actionitems").select("*").eq("defaulter_id", defaulter_id).eq("category", "customer-requested").execute()

def approve_payment(payment_id: str):
    # Fetch payment details
    payment_result = get_payment(payment_id)
    if not payment_result.data:
        raise ValueError(f"Payment {payment_id} not found")
    
    payment = payment_result.data[0]
    
    # Update the payment to approved status
    update_payment(payment_id, _approved="approved")
    
    return f"Payment {payment_id} has been approved."

def get_payments():
    return supabase.table("payment_links").select("*").execute()

def get_pending_payments():
    # Get payments that are specifically pending approval
    # Only return payments that are explicitly not approved or rejected
    result = supabase.table("payment_links").select("*").in_("_approved", ["pending", "pending_cancellation", None]).execute()
    # print(f"[DEBUG] Found {len(result.data) if result.data else 0} pending payments")
    if result.data:
        for payment in result.data[:3]:  # Show first 3 for debugging
            # print(f"[DEBUG] Payment {payment['id']}: _approved={payment.get('_approved')}, amount={payment.get('amount')}, issue_id={payment.get('issue_id')}")
            pass
    return result

def get_approved_payments():
    return supabase.table("payment_links").select("*").eq("_approved", "approved").execute()

def reject_payment(payment_id: str):
    return (
        supabase.table("payment_links")
        .update({"_approved": "rejected"})
        .eq("id", payment_id)
        .execute()
    )

def mark_payment_as_paid(payment_id: str):
    """Mark a payment as paid (for testing/manual updates)"""
    return execute_with_retry(
        lambda: supabase.table("payment_links")
        .update({"status": "paid"})
        .eq("id", payment_id)
        .execute()
    )


def mark_action_items_pending(defaulter_id: str):
    supabase.table("defaulters").update({"paused": True}).eq("id", defaulter_id).execute()
    # return (
    #     supabase.table("actionitems")
    #     .update({"_approved": False})
    #     .eq("defaulter_id", defaulter_id)
    #     .execute()
    # )

def mark_action_items_approved(defaulter_id: str):
    return (
        supabase.table("actionitems")
        .update({"_approved": True})
        .eq("defaulter_id", defaulter_id)
        .execute()
    )

def get_action_items(date: str, from_time: str, to_time: str):
    """
    Get action items where:
    - action_date matches the given date
    - action_time is between from_time and to_time
    - the associated defaulter is not paused

    Args:
        date (str): The date to filter action items by (YYYY-MM-DD)
        from_time (str): The start time to filter by (HH:MM:SS)
        to_time (str): The end time to filter by (HH:MM:SS)

    Returns:
        List of action items that match the criteria
    """
    return (
        supabase.table("actionitems")
        .select("*, defaulters!inner(*)")
        .eq("action_date", date)
        .gte("action_time", from_time)
        .lte("action_time", to_time)
        .eq("defaulters.paused", False)
        .execute()
    )

def json_serialize_dates(data):
    for k, v in data.items():
        if hasattr(v, "isoformat"):
            data[k] = v.isoformat()
    return data

def update_payment(payment_id: str, **kwargs):
    """
    Update payment_links with any provided fields. This is the single update function for payment_links.
    """
    update_data = {}
    for k, v in kwargs.items():
        update_data[k] = v
    if not update_data:
        raise ValueError("No valid fields provided for update")
    update_data = json_serialize_dates(update_data)
    return (
        supabase.table("payment_links")
        .update(update_data)
        .eq("id", payment_id)
        .execute()
    )

def delete_payment(payment_id: str):
    """Delete a payment from the payment_links table"""
    return (
        supabase.table("payment_links")
        .delete()
        .eq("id", payment_id)
        .execute()
    )

def update_payment_status(payment_id: str, status: str):
    return (
        supabase.table("payment_links")
        .update({"status": status})
        .eq("id", payment_id)
        .execute()
    )

def insert_call_comm(call_id: str, audio_recording_url: str, call_duration: int, ended_reason: str):
    # print(f"[DB] Inserting into calllogs: ended_reason={ended_reason}")
    return (
        supabase.table("calllogs")
        .insert({
            "id": call_id,
            "audio_recording_url": audio_recording_url,
            "call_duration": call_duration,
            "ended_reason": ended_reason
        })
        .execute()
    )

def get_scheduled_payment_plans_by_defaulter_id(defaulter_id: str):
    """
    Get all scheduled payment plans for a specific defaulter by joining with the issues table.

    Args:
        defaulter_id (str): The ID of the defaulter

    Returns:
        List of payment plans associated with the defaulter
    """
    return (
        supabase.table("payment_links")
        .select("*, issues!inner(defaulter_id)")
        .eq("issues.defaulter_id", defaulter_id)
        .in_("_approved", ["active", "pending", "approved", "pending_approval"])
        .execute()
    )

def get_payment_plans_by_issue_id(issue_id: str):
    return supabase.table("payment_links").select("*").eq("issue_id", issue_id).execute()

def get_transaction_history_with_details(start_date=None, end_date=None, debtor_id=None, status='paid', limit=50, offset=0, search='', approved_only=False):
    """
    Get transaction history with debtor details, filtering and pagination
    """
    query = supabase.table("payment_links").select("""
        *,
        issues!inner(
            issue_id,
            defaulter_id,
            outstanding_amount,
            status,
            defaulters!inner(
                id,
                name,
                email,
                phone,
                customizations
            )
        )
    """)

    # Filter by approved only if requested
    if approved_only:
        query = query.eq("_approved", "approved")

    # Apply status filter - handle multiple statuses
    if status:
        if ',' in status:
            status_list = [s.strip() for s in status.split(',')]
            query = query.in_("status", status_list)
        else:
            query = query.eq("status", status)

    # Apply filters
    if start_date:
        query = query.gte("created_at", start_date)
    if end_date:
        query = query.lte("created_at", end_date)
    if debtor_id:
        query = query.eq("issues.defaulter_id", debtor_id)
    if search:
        query = query.or_(f"issues.defaulters.name.ilike.%{search}%,issues.defaulters.email.ilike.%{search}%,issues.defaulters.phone.ilike.%{search}%,id.ilike.%{search}%,transaction_id.ilike.%{search}%")

    query = query.order("created_at", desc=True).range(offset, offset + limit - 1)

    return execute_with_retry(lambda: query.execute())

def get_transaction_count(start_date=None, end_date=None, debtor_id=None, status='paid', search='', approved_only=False):
    """
    Get total count of transactions matching the filters
    """
    query = supabase.table("payment_links").select("id", count="exact")

    # Filter by approved only if requested
    if approved_only:
        query = query.eq("_approved", "approved")

    # Apply status filter - handle multiple statuses
    if status:
        if ',' in status:
            status_list = [s.strip() for s in status.split(',')]
            query = query.in_("status", status_list)
        else:
            query = query.eq("status", status)

    if start_date:
        query = query.gte("created_at", start_date)
    if end_date:
        query = query.lte("created_at", end_date)
    if debtor_id:
        query = query.eq("issues.defaulter_id", debtor_id)
    if search:
        query = query.select("""
            id,
            transaction_id,
            issues!inner(
                defaulters!inner(
                    name,
                    email,
                    phone
                )
            )
        """, count="exact").or_(f"issues.defaulters.name.ilike.%{search}%,issues.defaulters.email.ilike.%{search}%,issues.defaulters.phone.ilike.%{search}%,id.ilike.%{search}%,transaction_id.ilike.%{search}%")

    result = execute_with_retry(lambda: query.execute())
    return result.count if result.count is not None else 0

def get_payment_summary_stats(start_date=None, end_date=None):
    """
    Get summary statistics for payments
    """
    # Base query for paid payments (for total collected amount)
    paid_query = supabase.table("payment_links").select("amount", count="exact").eq("status", "paid")

    if start_date:
        paid_query = paid_query.gte("created_at", start_date)
    if end_date:
        paid_query = paid_query.lte("created_at", end_date)

    paid_result = execute_with_retry(lambda: paid_query.execute())

    # Calculate total collected amount (only from paid transactions)
    total_amount = sum(payment.get("amount", 0) for payment in paid_result.data) if paid_result.data else 0

    # Get ALL transactions count (regardless of status)
    all_transactions_query = supabase.table("payment_links").select("id", count="exact")
    if start_date:
        all_transactions_query = all_transactions_query.gte("created_at", start_date)
    if end_date:
        all_transactions_query = all_transactions_query.lte("created_at", end_date)

    all_transactions_result = execute_with_retry(lambda: all_transactions_query.execute())
    total_transactions = all_transactions_result.count if all_transactions_result.count is not None else 0

    # Get in-progress payments count (approved but not yet paid)
    inprogress_query = supabase.table("payment_links").select("amount", count="exact").eq("status", "approved")
    if start_date:
        inprogress_query = inprogress_query.gte("created_at", start_date)
    if end_date:
        inprogress_query = inprogress_query.lte("created_at", end_date)

    inprogress_result = execute_with_retry(lambda: inprogress_query.execute())
    inprogress_amount = sum(payment.get("amount", 0) for payment in inprogress_result.data) if inprogress_result.data else 0
    inprogress_transactions = inprogress_result.count if inprogress_result.count is not None else 0

    # Calculate average of successful payments (paid transactions only)
    paid_transactions_count = paid_result.count if paid_result.count is not None else 0

    return {
        "total_collected": total_amount,
        "total_transactions": total_transactions,
        "pending_amount": inprogress_amount,  # In-progress payments (approved but not paid)
        "pending_transactions": inprogress_transactions,
        "average_transaction": total_amount / paid_transactions_count if paid_transactions_count > 0 else 0
    }

def get_comm_stats(org_id: str, start_date=None, end_date=None):
    # Initialize result with default values
    result = {"calls": 0, "emails": 0,"call_duration": 0}

    # Get total call count from commlogs for this org
    call_count_query = supabase.table("commlogs") \
        .select("*", count="exact") \
        .eq("channel", "call")
    call_count_response = call_count_query.execute()
    result["calls"] = call_count_response.count or 0

    # Get actual call durations from calllogs table
    call_duration_query = supabase.table("calllogs").select("call_duration")
    call_duration_response = call_duration_query.execute()
    # print(f"Call duration response: {call_duration_response.data}")
    total_duration = 0
    if call_duration_response.data:
        total_duration = sum(
            call["call_duration"] for call in call_duration_response.data if call.get("call_duration")
        )
    result["call_duration"] = total_duration
    # print(f"Call duration: {result['call_duration']}")

    # Get inbound emails count
    inbound_emails_query = supabase.table("commlogs").select("*", count="exact") \
        .eq("channel", "email").eq("direction", "inbound")
    inbound_emails_response = inbound_emails_query.execute()
    inbound_emails = inbound_emails_response.count or 0

    # Get outbound emails count
    outbound_emails_query = supabase.table("commlogs").select("*", count="exact") \
        .eq("channel", "email").eq("direction", "outbound")
    outbound_emails_response = outbound_emails_query.execute()
    outbound_emails = outbound_emails_response.count or 0

    result["inbound_emails"] = inbound_emails
    result["outbound_emails"] = outbound_emails
    result["emails"] = inbound_emails + outbound_emails

    # Get promise to pay count (issues in 'in-progress' state)
    promise_to_pay_query = supabase.table("issues").select("*", count="exact") \
        .eq("status", "in-progress")
    promise_to_pay_response = promise_to_pay_query.execute()
    result["promise_to_pay"] = promise_to_pay_response.count or 0

    return result

def mark_defaulter_as_paused(defaulter_id: str):
    return (
        supabase.table("defaulters")
        .update({"paused": True})
        .eq("id", defaulter_id)
        .execute()
    )

def mark_defaulter_as_active(defaulter_id: str):
    return (
        supabase.table("defaulters")
        .update({"paused": False})
        .eq("id", defaulter_id)
        .execute()
    )

def get_payment_link_details(payment_id: str):
    """
    Get detailed payment link information including all related data
    """
    return execute_with_retry(
        lambda: supabase.table("payment_links").select("""
            *,
            issues!inner(
                issue_id,
                defaulter_id,
                outstanding_amount,
                status,
                defaulters!inner(
                    id,
                    name,
                    email,
                    phone,
                    customizations
                )
            )
        """).eq("id", payment_id).execute()
    )

def update_payment_link_status(payment_id: str, status: str, transaction_id: str = None, notes: str = None):
    """
    Update payment link status with optional transaction ID and notes
    Note: notes field may not exist in payment_links table, so we'll skip it for now
    """
    update_data = {"status": status}
    if transaction_id:
        update_data["transaction_id"] = transaction_id
    # Skip notes for now as the field may not exist
    # if notes:
    #     update_data["notes"] = notes

    return execute_with_retry(
        lambda: supabase.table("payment_links")
        .update(update_data)
        .eq("id", payment_id)
        .execute()
    )

def get_transactions_by_status(status: str, limit: int = 50):
    """
    Get all transactions by status for admin management
    """
    return execute_with_retry(
        lambda: supabase.table("payment_links").select("""
            id,
            created_at,
            amount,
            status,
            transaction_id,
            _approved,
            issues!inner(
                defaulter_id,
                defaulters!inner(
                    name,
                    email,
                    phone
                )
            )
        """).eq("status", status).order("created_at", desc=True).limit(limit).execute()
    )

def get_payment_analytics(start_date=None, end_date=None):
    """
    Get comprehensive payment analytics from payment_links table
    """
    # Base query
    base_query = supabase.table("payment_links").select("amount, status, created_at, recurring, settlement_discount")

    if start_date:
        base_query = base_query.gte("created_at", start_date)
    if end_date:
        base_query = base_query.lte("created_at", end_date)

    result = execute_with_retry(lambda: base_query.execute())

    if not result.data:
        return {
            "total_payments": 0,
            "total_amount": 0,
            "paid_amount": 0,
            "pending_amount": 0,
            "failed_amount": 0,
            "recurring_payments": 0,
            "one_time_payments": 0,
            "settlement_discounts": 0,
            "average_payment": 0,
            "success_rate": 0
        }

    payments = result.data
    total_payments = len(payments)
    total_amount = sum(p.get("amount", 0) for p in payments)

    paid_payments = [p for p in payments if p.get("status") == "paid"]
    pending_payments = [p for p in payments if p.get("status") == "pending"]
    failed_payments = [p for p in payments if p.get("status") == "failed"]

    paid_amount = sum(p.get("amount", 0) for p in paid_payments)
    pending_amount = sum(p.get("amount", 0) for p in pending_payments)
    failed_amount = sum(p.get("amount", 0) for p in failed_payments)

    recurring_payments = len([p for p in payments if p.get("recurring")])
    settlement_discounts = len([p for p in payments if p.get("settlement_discount")])

    return {
        "total_payments": total_payments,
        "total_amount": total_amount,
        "paid_amount": paid_amount,
        "pending_amount": pending_amount,
        "failed_amount": failed_amount,
        "recurring_payments": recurring_payments,
        "one_time_payments": total_payments - recurring_payments,
        "settlement_discounts": settlement_discounts,
        "average_payment": total_amount / total_payments if total_payments > 0 else 0,
        "success_rate": (len(paid_payments) / total_payments * 100) if total_payments > 0 else 0
    }

def get_calllog_by_id(call_id: str):
    return supabase.table("calllogs").select("*").eq("id", call_id).single().execute()

def get_restrictions_by_defaulter_id(defaulter_id: str):
    return supabase.table("restrictions").select("*").eq("defaulter_id", defaulter_id).execute()

def upsert_restrictions(defaulter_id: str, description: str):
    return supabase.table("restrictions").upsert({
        "defaulter_id": defaulter_id,
        "description": description
    }).execute()

def insert_payment_duplicate(amount: int, issue_id: int, recurring: bool = False, approved: bool = False, settlement_discount: bool = False, stripe_invoice_id: str = None, url: str = None, type: str = None, interval: str = None, interval_count: int = None, due_date: str = None, start_date: str = None):
   
    existing_payment = supabase.table("payment_links").select("id, _approved, amount, status, stripe_invoice_id, url").eq("issue_id", issue_id).eq("amount", amount).eq("recurring", recurring).in_("_approved", ["pending", "approved", None]).execute()
    
    
    paid_payment = supabase.table("payment_links").select("id, _approved, amount, status").eq("issue_id", issue_id).eq("amount", amount).eq("recurring", recurring).eq("status", "paid").execute()
    
    if existing_payment.data:
        existing_record = existing_payment.data[0]
        existing_id = existing_record['id']
        
        # Check if we need to update with new stripe_invoice_id or url
        needs_update = False
        update_data = {}
        
        if stripe_invoice_id and not existing_record.get('stripe_invoice_id'):
            update_data['stripe_invoice_id'] = stripe_invoice_id
            needs_update = True
            # print(f"[UPDATE] Adding stripe_invoice_id {stripe_invoice_id} to existing payment {existing_id}")
        
        if url and not existing_record.get('url'):
            update_data['url'] = url
            needs_update = True
            # print(f"[UPDATE] Adding url to existing payment {existing_id}")
        
        if needs_update:
            # Update the existing payment with the new data
            update_result = supabase.table("payment_links").update(update_data).eq("id", existing_id).execute()
            # print(f"[UPDATE] Successfully updated payment {existing_id} with stripe_invoice_id: {stripe_invoice_id}")
            return update_result
        else:
            # print(f"[DUPLICATE] Prevented duplicate payment: existing {existing_id} for issue {issue_id}, amount ${amount/100:.2f}, approval_status: {existing_record['_approved']}")
            return existing_payment
    
    if paid_payment.data:
        # print(f"[DUPLICATE] Prevented duplicate payment: existing paid payment {paid_payment.data[0]['id']} for issue {issue_id}, amount ${amount/100:.2f}")
        return paid_payment  
    
    # For recurring payments, do not set due_date
    if recurring:
        due_date = None
    insert_data = {
        "amount": amount,
        "issue_id": issue_id,
        "recurring": recurring,
        "_approved": "pending" if not approved else "approved",
        "settlement_discount": settlement_discount,
        "stripe_invoice_id": stripe_invoice_id,
        "url": url,
        "type": type,
        "due_date": due_date,
        "start_date": start_date
    }
    
    # Note: interval and interval_count are not stored in payment_links table
    # They will be used during approval process when creating Stripe subscriptions
    # For now, we store them in a JSON field or handle them separately
        
    return (
        supabase.table("payment_links")
        .insert(insert_data)
        .execute()
    )

def get_payment_duplicate(payment_id):
    return execute_with_retry(
        lambda: supabase.table("payment_links").select("*").eq("id", payment_id).execute()
    )

def get_payments_duplicate():
    return supabase.table("payment_links").select("*").execute()

def get_payments_by_issue_id_duplicate(issue_id: int):
    return supabase.table("payment_links").select("*").eq("issue_id", issue_id).execute()

def get_existing_payment_by_issue_amount(issue_id: int, amount_cents: int, recurring: bool):
    """
    Check if a payment record already exists for this issue/amount combination
    """
    return supabase.table("payment_links").select("id").eq("issue_id", issue_id).eq("amount", amount_cents).eq("recurring", recurring).execute()

def get_payment_link_by_id(payment_link_id: str):
    """
    Get payment link by ID
    """
    return supabase.table("payment_links").select("*").eq("id", payment_link_id).execute()

def get_payment_by_stripe_invoice_id(stripe_invoice_id: str):
    """
    Get payment by Stripe invoice ID
    """
    return supabase.table("payment_links").select("*").eq("stripe_invoice_id", stripe_invoice_id).execute()

def get_payment_by_stripe_subscription_id(stripe_subscription_id: str):
    """
    Get payment by Stripe subscription ID
    """
    return supabase.table("payment_links").select("id").eq("stripe_subscription_id", stripe_subscription_id).execute()

def get_sample_payments(limit: int = 5):
    """
    Get sample payments for debugging
    """
    return supabase.table("payment_links").select("id, stripe_invoice_id").limit(limit).execute()

def get_customer_requested_followups(limit: int = 20):
    """
    Get customer-requested follow-ups for notifications
    """
    return supabase.table("actionitems").select("id, action_date, action_time, action_reason, is_human_followup, defaulter_id").eq("category", "customer-requested").order("action_date", desc=True).limit(limit).execute()

def get_pending_payment_approvals(limit: int = 20):
    """
    Get pending payment approvals for notifications
    """
    return supabase.table("payment_links").select("id, amount, status, created_at, issue_id").eq("_approved", "pending").order("created_at", desc=True).limit(limit).execute()

def get_issue_with_defaulter_by_issue_id(issue_id: str):
    """
    Get issue with defaulter information by issue ID
    """
    return supabase.table("issues").select("defaulter_id, defaulters!inner(*)").eq("issue_id", issue_id).execute()

def get_defaulters_basic_info(limit: int = 100):
    """
    Get basic defaulter information for listing
    """
    return supabase.table("defaulters").select("id, name, email, phone").limit(limit).execute()

def get_chatlogs_by_defaulter_id(defaulter_id: str):
    """
    Get chat logs by defaulter ID ordered by timestamp
    """
    return supabase.table('chatlogs').select('*').eq('defaulter_id', defaulter_id).order('timestamp').execute()

def insert_notification(defaulter_id, text, is_read=False, is_done=False, is_canceled=False):
    notif = {
        "id": str(uuid.uuid4()),
        "defaulter_id": defaulter_id,
        "text": text,
        "is_read": is_read,
        "is_done": is_done,
        "is_canceled": is_canceled,
    }
    return supabase.table("notifications").insert(notif).execute()
