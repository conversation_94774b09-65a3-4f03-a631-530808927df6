from app.utils.supabase.queries import (
    get_payment_plans_by_issue_id,
    get_org,
    get_comm_logs_by_defaulter_id,
    get_defaulter_by_id,
    update_customizations,
    get_issues_by_defaulter_id,
    get_emails_by_id,
    get_customer_requested_comms,
)
from datetime import datetime, timedelta
import json
from app.utils import hash_sensitive_data

# def get_scheduled_payment_plans(defaulter_id):
#     return get_scheduled_payment_plans_by_defaulter_id(defaulter_id).data


def sort_payment_plans_by_created_at(issue_id):
    # sort by created_at oldest to newest
    plans = get_payment_plans_by_issue_id(issue_id)
    return sorted(plans.data, key=lambda x: x["created_at"])


def get_comm_history_mixed(defaulter_id):
    comm_logs = get_comm_logs_by_defaulter_id(defaulter_id).data
    if not comm_logs:
        return []

    mixed_history = []
    email_ids = []

    # Separate email and call logs
    for comm in comm_logs:
        if comm["channel"] == "email":
            email_ids.append(comm["comm_id"])
        elif comm["channel"] == "call":
            # For calls, we use the summary field
            mixed_history.append(
                {
                    "type": "call",
                    "summary": comm["summary"],
                    "content": None,  # Calls don't have content
                    "timestamp": comm["timestamp"],
                    "direction": comm["direction"],
                }
            )

    # Get email transcripts
    if email_ids:
        emails = get_emails_by_id(email_ids).data
        for email in emails:
            mixed_history.append(
                {
                    "type": "email",
                    "summary": None,  # Emails don't have summaries
                    "content": email["email_body"],
                    "timestamp": email["timestamp"],
                    "direction": email["direction"],
                }
            )

    # Sort by timestamp
    mixed_history.sort(key=lambda x: x["timestamp"])
    return mixed_history


def serialize_datetime(obj):
    if isinstance(obj, datetime):
        return obj.isoformat()
    raise TypeError(f"Type {type(obj)} not serializable")


def deserialize_datetime(dt_str):
    if isinstance(dt_str, str):
        return datetime.fromisoformat(dt_str)
    return dt_str


def make_caller_inputs(defaulter_id):
    case = get_defaulter_by_id(defaulter_id).data[0]
    issues = get_issues_by_defaulter_id(defaulter_id).data
    case["cases"] = issues
    org = get_org().data[0]
    promise_failure_policy = org["metadata"]["promise_failure_policy"]

    result = {
        "case_info": {k: v for k, v in case.items() if k != "customizations"},
        "strategy": case["customizations"].get("negotiation_strategy"),
        "commlogs": get_comm_logs_by_defaulter_id(defaulter_id).data,
        "promise_failure_policy": promise_failure_policy,
    }

    upcoming_comms = get_customer_requested_comms(defaulter_id).data
    if upcoming_comms:
        result["upcoming_comms"] = upcoming_comms

    return result


def make_email_composer_inputs(defaulter_id):
    case_info = get_defaulter_by_id(defaulter_id).data[0]
    issues = get_issues_by_defaulter_id(defaulter_id).data
    case_info["cases"] = issues
    org = get_org().data[0]

    result = {
        "case_info": {k: v for k, v in case_info.items() if k != "customizations"},
        "strategy": case_info["customizations"].get("negotiation_strategy"),
        "commlogs": get_comm_history_mixed(defaulter_id),
        "promise_failure_policy": org["metadata"]["promise_failure_policy"],
    }

    upcoming_comms = get_customer_requested_comms(defaulter_id).data
    if upcoming_comms:
        result["upcoming_comms"] = upcoming_comms

    return result


def verify_user_call(defaulter_id, verification_type=None, verification_value=None):
    case = get_defaulter_by_id(defaulter_id).data[0]

    if verification_type == "ssn":
        return case["customizations"]["ssn"] == hash_sensitive_data(verification_value)
    elif verification_type == "dob":
        return case["customizations"]["dob"] == hash_sensitive_data(verification_value)
    else:
        return False


def is_verified_email(defaulter_id):
    org = get_org().data[0]
    if org["metadata"]["require_verification_email"] == False:
        print("Verification email is not required, returning True")
        return True

    case = get_defaulter_by_id(defaulter_id).data[0]
    try:
        email_last_verified = deserialize_datetime(
            case["customizations"]["email_last_verified"]
        )
        return email_last_verified and email_last_verified > datetime.now() - timedelta(
            days=1
        )
    except Exception:
        return False


def verify_user_email(defaulter_id, verification_type=None, verification_value=None):
    org = get_org().data[0]
    if org["metadata"]["require_verification_email"] == False:
        print("Verification email is not required, returning True")
        return True

    case = get_defaulter_by_id(defaulter_id).data[0]

    if verification_type == "ssn":
        if case["customizations"]["ssn"] == hash_sensitive_data(verification_value):
            update_customizations(
                defaulter_id,
                {"email_last_verified": serialize_datetime(datetime.now())},
            )
            return True
    elif verification_type == "dob":
        if case["customizations"]["dob"] == hash_sensitive_data(verification_value):
            update_customizations(
                defaulter_id,
                {"email_last_verified": serialize_datetime(datetime.now())},
            )
            return True
    return False


def get_payment_status_instructions(status):
    if status == "pending":
        return "This payment has been scheduled but has not yet been paid."
    elif status == "in-progress":
        return "This recurring payment has been started but has not finished."
    elif status == "overdue":
        return "This account has a overdue payment. Remind the customer of this promise, resolve any problems, and set up a new payment arrangement."
    elif status == "failed":
        return "A payment for this account has failed to process. Problem solve with the customer and set up a new payment arrangement."
    elif status == "paid":
        return "This payment has been paid."
    # else:
    #     raise ValueError(f"Invalid issue status: {status}")
    else:
        return "Error: Invalid payment status."


def get_issue_status_instructions(status, promise_failure_policy):
    if status == "unsolved":
        return "Request payment and negotiate"
    elif status == "in-progress":
        return "This issue has had payments scheduled, but it has not been fully resolved. Make sure there is a path to resolution and if there is a monthly payment plan, check if the customer would like to increase payment amount."
    elif status == "promise-failed":
        return (
            "This account has a problematic payment arrangement. See this organization's promise failure policy for more information.\n\nPROMISE FAILURE POLICY ============= \n"
            + promise_failure_policy
            + "\n=============\n"
        )
    else:
        return f"Error: Invalid issue status {status}."


def format_email_args_readable(args):
    promise_failure_policy = args.get("promise_failure_policy", "")
    inputs = "Case Info:\n"
    for issue in args["case_info"].get("cases", []):
        inputs += f"\nAccount {issue['issue_id']} =========\n"
        inputs += f"issue_id: {issue['issue_id']}\n"
        inputs += f"Current Outstanding Amount: {issue['outstanding_amount']}\n"
        inputs += f"Delinquency Date: {issue['delinquency_date']}\n"
        inputs += f"Status: {get_issue_status_instructions(issue['status'], promise_failure_policy)}\n"
        inputs += f"Additional Data: {str(issue.get('metadata', {}))}\n"
        inputs += "Payment Arrangements:\n"
        i = 1
        for payment_plan in sort_payment_plans_by_created_at(issue["issue_id"]):
            approval_status = "pending - awaiting approval from manager, link will be sent upon approval"
            if payment_plan["_approved"] == "approved":
                approval_status = "approved"
            elif payment_plan["_approved"] == "rejected":
                approval_status = "rejected"

            inputs += f"{i}. Payment Arrangement ({approval_status}):\n"
            inputs += f"  - Amount: ${payment_plan['amount'] / 100}\n"
            inputs += f"  - Proposal Date: {payment_plan['created_at'][:10]}\n"
            inputs += f"  - Payment Status: {payment_plan['status']} : {get_payment_status_instructions(payment_plan['status'])}\n"
            if payment_plan["_approved"] == "approved":
                inputs += f"  - Link: https://sscd05m3-5000.use.devtunnels.ms/v0/pay/{payment_plan['id']}\n"
            i += 1
        inputs += "=========\n"

    # if args.get("strategy"):
    #     inputs += f"\nThe negotiation strategy is:\n{args['strategy']}\n"
    # else:
    #     inputs += "\nNo negotiation strategy provided.\n"

    # Format communication history
    comm_history = args.get("commlogs", [])
    inputs += "\n============= Begin Communication History ==============\n"
    if comm_history:
        for comm in comm_history:
            if comm["type"] == "call":
                inputs += f"Call ({comm['direction']}) at {comm['timestamp']}:\nSummary: {comm['summary']}\n"
            elif comm["type"] == "email":
                inputs += f"Email ({comm['direction']}) at {comm['timestamp']}:\nContent: {comm['content']}\n"
    else:
        inputs += "There has been no communication with this customer. This is the first contact.\n"
    inputs += "============= End of Communication History ==============\n"

    if args.get("upcoming_comms"):
        inputs += f"The defaulter has requested the following communications:"
        for comm in args["upcoming_comms"]:
            inputs += f"- {comm['action_channel']} on {comm['action_date']} at {comm['action_time']}:\n"
            inputs += f"  - Purpose: {comm['action_reason']}\n"
        inputs += f"If the conversation makes an item obsolete, ask the user if they would still like to receive it."

    if args.get("reason"):
        inputs += f"\nThe purpose of this communication is: {args['reason']}"

    return inputs


def format_caller_inputs_readable(data):
    # Parse nested case_info if it's a string representation of a dict
    if isinstance(data.get("case_info"), str):
        try:
            data["case_info"] = eval(data["case_info"])
            # pop customizations from case_info
            data["case_info"].pop("customizations", None)
        except Exception:
            pass

    promise_failure_policy = data.get("promise_failure_policy", "")

    output = "# Most Recent Case Information:\n"
    for issue in data["case_info"].get("cases", []):
        output += f"\nAccount {issue['issue_id']} =========\n"
        output += f"issue_id: {issue['issue_id']}\n"
        output += f"**Outstanding Amount: {issue['outstanding_amount']}**\n"
        output += f"Delinquency Date: {issue['delinquency_date']}\n"
        output += f"Status: {get_issue_status_instructions(issue['status'], promise_failure_policy)}\n"
        output += f"Additional Data: {str(issue.get('metadata', {}))}\n\n"
        
        output += "## Payment Arrangements:\n"
        i = 1
        for payment_plan in sort_payment_plans_by_created_at(issue["issue_id"]):
            approval_status = "pending - awaiting approval from manager, link will be sent upon approval"
            if payment_plan["_approved"] == "approved":
                approval_status = "approved"
            elif payment_plan["_approved"] == "rejected":
                approval_status = "rejected"

            output += f"{i}. Payment Arrangement ({approval_status}):\n"
            output += f"  - Amount: ${payment_plan['amount'] / 100}\n"
            output += f"  - Date Proposed: {payment_plan['created_at'][:10]}\n"
            output += f"  - Payment Status: {payment_plan['status']}\n"
            output += f"  - Instructions: {get_payment_status_instructions(payment_plan['status'])}\n"
            # output += f"  - Link: https://sscd05m3-5000.use.devtunnels.ms/v0/pay/{payment_plan['id']}\n"
            i += 1
        output += "=========\n\n"

    # # Format output
    # output += "\nNegotiation Strategy:\n"
    # output += data["strategy"]

    output += "##Communication History:\n"
    for comm in data["commlogs"]:
        output += (
            f"{comm['channel'].upper()} ({comm['direction']}) - {comm['timestamp']}:\n"
        )
        output += f"Summary: {comm['summary']}\n"
        output += "-" * 10 + "\n"

    if data.get("upcoming_comms"):
        output += f"The defaulter has requested the following communications:"
        for comm in data["upcoming_comms"]:
            output += f"- {comm['action_channel']} on {comm['action_date']} at {comm['action_time']}:\n"
            output += f"  - Purpose: {comm['action_reason']}\n"
        output += f"If the conversation makes an item possibly obsolete, ask the user if they still want it.."

    if data.get("reason"):
        output += f"\nThe purpose of this communication is: {data['reason']}"

    return output


def get_info_email(defaulter_id):
    if get_org().data[0]["metadata"]["verification_types"] == []:
        inputs = make_email_composer_inputs(defaulter_id)
        return format_email_args_readable(inputs)
    elif is_verified_email(defaulter_id):
        inputs = make_email_composer_inputs(defaulter_id)
        return format_email_args_readable(inputs)
    else:
        return "The sender's identity has not been verified. Proceed verifying the user's identity using the 'try_verify' tool."


def try_verify_email(defaulter_id, verification_type, verification_value):
    if verify_user_email(defaulter_id, verification_type, verification_value):
        return get_info_email(defaulter_id)
    else:
        return "The sender's identity has not been verified. Proceed verifying the sender's identity by the verification method(s) provided."


def get_info_call(defaulter_id, verification_type=None, verification_value=None):
    if get_org().data[0]["metadata"]["verification_types"] == []:
        inputs = make_caller_inputs(defaulter_id)
        return format_caller_inputs_readable(inputs)
    elif verify_user_call(defaulter_id, verification_type, verification_value):
        inputs = make_caller_inputs(defaulter_id)
        return format_caller_inputs_readable(inputs)
    else:
        return "Alert: Verification failed.The caller's identity is not verified. Please verify the caller's identity before proceeding."
